{"FileVersion": 3, "Version": 1, "VersionName": "0.2", "FriendlyName": "Concert Sync - Server", "Description": "Server plugin to enables multi-users editor sessions", "Category": "Networking", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "Hidden": true, "IsBetaVersion": true, "Installed": false, "SupportedPrograms": ["UnrealMultiUserServer", "CoopMultiUserServer", "UnrealMultiUserSlateServer", "UnrealRecoverySvc", "CrashReportClientEditor"], "Modules": [{"Name": "ConcertSyncServer", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "ProgramAllowList": ["UnrealMultiUserServer", "CoopMultiUserServer", "UnrealMultiUserSlateServer", "UnrealRecoverySvc", "CrashReportClientEditor"]}], "Plugins": [{"Name": "ConcertMain", "Enabled": true}, {"Name": "ConcertSyncCore", "Enabled": true}]}