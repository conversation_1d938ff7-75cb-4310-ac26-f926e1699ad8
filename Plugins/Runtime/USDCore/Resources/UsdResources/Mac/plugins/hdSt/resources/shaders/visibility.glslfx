-- glslfx version 0.1

//
// Copyright 2018 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/visibility.glslfx

--- --------------------------------------------------------------------------
-- glsl Visibility.Fragment.Fallback
void DiscardBasedOnTopologicalVisibility()
{
    // Nothing to do, since there's no authored opinion.
}

--- --------------------------------------------------------------------------
-- glsl Visibility.Fragment.Topology

FORWARD_DECL(int GetElementID()); // code gen

void GetBitmaskBufferIndices(
    int id, REF(thread, int) arrayIndex, REF(thread, int) bitIndex)
{
    arrayIndex = id / 32;
    bitIndex   = id % 32;
}

bool IsBitSet(uint bitmask, int bitIndex)
{
    return bool(bitmask & (1 << bitIndex));
}

bool IsElementVisible()
{
#if defined(HD_HAS_elementsVisibility)
    // Element (face) visibility is encoded as an array of bitmasks (uint32)
    // with 1 bit per authored face.
    int elementId = GetElementID();
    // When rendering a mesh as points, element id doesn't make sense. Code
    // gen returns -1 as a fallback for this case.
    if (elementId != -1) {
        int arrayIndex, bitIndex;
        GetBitmaskBufferIndices(elementId, arrayIndex, bitIndex);
        uint ev = HdGet_elementsVisibility(arrayIndex);
        return IsBitSet(ev, bitIndex);
    }
#endif
    return true;
}

FORWARD_DECL(int GetPointId()); // pointId.glslfx

bool IsPointVisible()
{
#if defined(HD_HAS_pointsVisibility)
    // Point visibility is encoded as an array of bitmasks (uint32) with 1 bit
    // per unrefined vertex.
    int pointId = GetPointId();
    // When *not* rendering a mesh as points, we return -1 for the point id.
    // See PointId.Fragment.Fallback
    if (pointId != -1) {
        int arrayIndex, bitIndex;
        GetBitmaskBufferIndices(pointId, arrayIndex, bitIndex);
        uint pv = HdGet_pointsVisibility(arrayIndex);
        return IsBitSet(pv, bitIndex);
    }
#endif
    return true;
}

void DiscardBasedOnTopologicalVisibility()
{
    if (!IsElementVisible() || !IsPointVisible()) {
        discard;
    }
}