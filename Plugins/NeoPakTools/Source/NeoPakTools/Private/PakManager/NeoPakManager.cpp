// Copyright Epic Games, Inc. All Rights Reserved.

#include "PakManager/NeoPakManager.h"
#include "NeoPakTools.h"
#include "Config/NeoPakToolsSettings.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Engine/Engine.h"

// 静态实例
UNeoPakManager* UNeoPakManager::Instance = nullptr;

UNeoPakManager* UNeoPakManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UNeoPakManager>();
        Instance->AddToRoot(); // 防止被垃圾回收
        Instance->DependencyResolver = MakeShared<FNeoAssetDependencyResolver>();
    }
    return Instance;
}

bool UNeoPakManager::PackageFromConfig(UNeoPakConfigAssetBase* ConfigAsset)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("ConfigAsset is null"));
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Starting packaging for config: %s"), *ConfigAsset->ConfigName);

#if WITH_EDITOR
    // 验证配置
    if (!ConfigAsset->ValidateConfiguration())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Configuration validation failed for: %s"), *ConfigAsset->ConfigName);
        return false;
    }
#endif

    // 执行打包
    return ExecutePackaging(ConfigAsset);
}

bool UNeoPakManager::PackageFromConfigWithDependencyCheck(UNeoPakConfigAssetBase* ConfigAsset, bool bAutoFixDependencies)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("ConfigAsset is null"));
        return false;
    }

    // 检查依赖关系
    if (DependencyResolver)
    {
        FNeoDependencyCheckResult DependencyResult = DependencyResolver->CheckDataAssetDirectoryDependencies(ConfigAsset, true);
        
        if (!DependencyResult.bCheckPassed)
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Dependency check failed for config: %s"), *ConfigAsset->ConfigName);
            
            if (!bAutoFixDependencies)
            {
                return false;
            }
            
            // TODO: 实现自动修复依赖的逻辑
            UE_LOG(LogNeoPakTools, Warning, TEXT("Auto-fix dependencies is not yet implemented"));
        }
    }

    return PackageFromConfig(ConfigAsset);
}

bool UNeoPakManager::LoadPakFile(const FString& PakFilePath)
{
    if (PakFilePath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("PakFilePath is empty"));
        return false;
    }

    // 检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("PAK file does not exist: %s"), *PakFilePath);
        return false;
    }

    // 检查是否已经加载
    if (LoadedPaks.Contains(PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("PAK file already loaded: %s"), *PakFilePath);
        return true;
    }

    // TODO: 实现实际的PAK加载逻辑
    UE_LOG(LogNeoPakTools, Log, TEXT("Loading PAK file: %s"), *PakFilePath);

    // 创建PAK条目
    FNeoPakEntry PakEntry;
    PakEntry.PakFilePath = PakFilePath;
    PakEntry.LoadStatus = EPakLoadStatus::Loaded;
    PakEntry.LoadTime = FDateTime::Now();

    // 添加到已加载列表
    LoadedPaks.Add(PakFilePath, PakEntry);

    UE_LOG(LogNeoPakTools, Log, TEXT("Successfully loaded PAK file: %s"), *PakFilePath);
    return true;
}

bool UNeoPakManager::UnloadPakFile(const FString& PakFilePath)
{
    if (!LoadedPaks.Contains(PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("PAK file not loaded: %s"), *PakFilePath);
        return false;
    }

    // TODO: 实现实际的PAK卸载逻辑
    UE_LOG(LogNeoPakTools, Log, TEXT("Unloading PAK file: %s"), *PakFilePath);

    // 从已加载列表中移除
    LoadedPaks.Remove(PakFilePath);

    UE_LOG(LogNeoPakTools, Log, TEXT("Successfully unloaded PAK file: %s"), *PakFilePath);
    return true;
}

TArray<FString> UNeoPakManager::GetMissingDependencies(const FString& PakFilePath)
{
    TArray<FString> MissingDependencies;

    // TODO: 实现依赖检查逻辑
    UE_LOG(LogNeoPakTools, Log, TEXT("Checking dependencies for PAK file: %s"), *PakFilePath);

    return MissingDependencies;
}

TArray<FNeoPakEntry> UNeoPakManager::GetLoadedPaks() const
{
    TArray<FNeoPakEntry> Result;
    LoadedPaks.GenerateValueArray(Result);
    return Result;
}

EPakLoadStatus UNeoPakManager::GetPakLoadStatus(const FString& PakFilePath) const
{
    if (const FNeoPakEntry* Entry = LoadedPaks.Find(PakFilePath))
    {
        return Entry->LoadStatus;
    }
    return EPakLoadStatus::NotLoaded;
}

bool UNeoPakManager::ExecutePackaging(UNeoPakConfigAssetBase* ConfigAsset)
{
    // 获取要打包的资产列表
    TArray<FSoftObjectPath> AssetsToPackage = ConfigAsset->GetAssetsToPackage();
    
    if (AssetsToPackage.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("No assets to package for config: %s"), *ConfigAsset->ConfigName);
        return false;
    }

    // 转换为字符串数组
    TArray<FString> AssetPaths;
    for (const FSoftObjectPath& AssetPath : AssetsToPackage)
    {
        AssetPaths.Add(AssetPath.ToString());
    }

    // 获取输出路径
    FString OutputPath = ConfigAsset->GetFullOutputPath();

    // 创建PAK文件
    return CreatePakFile(AssetPaths, OutputPath);
}

bool UNeoPakManager::CreatePakFile(const TArray<FString>& AssetPaths, const FString& OutputPath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Creating PAK file: %s"), *OutputPath);
    UE_LOG(LogNeoPakTools, Log, TEXT("Assets to package: %d"), AssetPaths.Num());

    for (const FString& AssetPath : AssetPaths)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("  - %s"), *AssetPath);
    }

    // TODO: 实现实际的PAK文件创建逻辑
    // 这里需要调用UnrealPak工具或使用引擎的PAK API

    UE_LOG(LogNeoPakTools, Log, TEXT("PAK file creation completed: %s"), *OutputPath);
    return true;
}

bool UNeoPakManager::ValidatePakFile(const FString& PakFilePath)
{
    // 检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("PAK file does not exist: %s"), *PakFilePath);
        return false;
    }

    // TODO: 实现PAK文件完整性验证
    UE_LOG(LogNeoPakTools, Log, TEXT("PAK file validation passed: %s"), *PakFilePath);
    return true;
}
