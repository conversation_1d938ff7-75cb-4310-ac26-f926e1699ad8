// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoDataAssetTypeRegistry.h"
#include "NeoPakTools.h"
#include "Config/NeoSkeletonPakConfig.h"
#include "Config/NeoCharacterPakConfig.h"
#include "Config/NeoAnimSequencePakConfig.h"
#include "Config/NeoAnimMontagePakConfig.h"
#include "Config/NeoClothingPakConfig.h"
#include "Config/NeoMapPakConfig.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// 静态常量定义
const FString FNeoDataAssetTypeRegistry::DataAssetTypeInfoFileName = TEXT("NeoPakTypeInfo.json");

void FNeoDataAssetTypeRegistry::RegisterDataAssetTypeInPak(const FString& PakFilePath, UNeoPakConfigAssetBase* ConfigAsset)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("ConfigAsset is null, cannot register type info"));
        return;
    }

    // 获取DataAsset类型信息
    FNeoDataAssetTypeInfo TypeInfo = GetDataAssetTypeInfo(ConfigAsset);

    // 序列化类型信息
    FString SerializedData = SerializeTypeInfo(TypeInfo);

    // 获取类型信息文件路径
    FString TypeInfoPath = GetTypeInfoFilePath(PakFilePath);

    // 确保目录存在
    FString Directory = FPaths::GetPath(TypeInfoPath);
    if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*Directory))
    {
        FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*Directory);
    }

    // 写入文件
    if (FFileHelper::SaveStringToFile(SerializedData, *TypeInfoPath))
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Registered DataAsset type info for PAK: %s, Type: %s"),
               *PakFilePath, *TypeInfo.TypeName);
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to save DataAsset type info to: %s"), *TypeInfoPath);
    }
}

FNeoDataAssetTypeInfo FNeoDataAssetTypeRegistry::GetDataAssetTypeFromPak(const FString& PakFilePath)
{
    FNeoDataAssetTypeInfo TypeInfo;

    // 获取类型信息文件路径
    FString TypeInfoPath = GetTypeInfoFilePath(PakFilePath);

    FString SerializedData;
    if (FFileHelper::LoadFileToString(SerializedData, *TypeInfoPath))
    {
        TypeInfo = DeserializeTypeInfo(SerializedData);
        UE_LOG(LogNeoPakTools, Log, TEXT("Loaded DataAsset type info from PAK: %s, Type: %s"),
               *PakFilePath, *TypeInfo.TypeName);
    }
    else
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("Failed to load DataAsset type info from: %s"), *TypeInfoPath);
    }

    return TypeInfo;
}

FNeoDataAssetTypeInfo FNeoDataAssetTypeRegistry::GetDataAssetTypeInfo(UNeoPakConfigAssetBase* ConfigAsset)
{
    FNeoDataAssetTypeInfo TypeInfo;

    if (!ConfigAsset)
    {
        return TypeInfo;
    }

    // 根据DataAsset类型设置信息
    if (Cast<UNeoSkeletonPakConfig>(ConfigAsset))
    {
        TypeInfo.TypeName = TEXT("Skeleton");
        TypeInfo.ClassPath = TEXT("/Script/NeoPakTools.NeoSkeletonPakConfig");
        TypeInfo.bRequiresSkeleton = false;
        TypeInfo.Description = TEXT("Skeleton Asset Package");
    }
    else if (Cast<UNeoCharacterPakConfig>(ConfigAsset))
    {
        TypeInfo.TypeName = TEXT("Character");
        TypeInfo.ClassPath = TEXT("/Script/NeoPakTools.NeoCharacterPakConfig");
        TypeInfo.bRequiresSkeleton = true;
        TypeInfo.Description = TEXT("Character Asset Package");
    }
    else if (Cast<UNeoAnimSequencePakConfig>(ConfigAsset))
    {
        TypeInfo.TypeName = TEXT("AnimSequence");
        TypeInfo.ClassPath = TEXT("/Script/NeoPakTools.NeoAnimSequencePakConfig");
        TypeInfo.bRequiresSkeleton = true;
        TypeInfo.Description = TEXT("Animation Sequence Package");
    }
    else if (Cast<UNeoAnimMontagePakConfig>(ConfigAsset))
    {
        TypeInfo.TypeName = TEXT("AnimMontage");
        TypeInfo.ClassPath = TEXT("/Script/NeoPakTools.NeoAnimMontagePakConfig");
        TypeInfo.bRequiresSkeleton = true;
        TypeInfo.Description = TEXT("Animation Montage Package");
    }
    else if (Cast<UNeoClothingPakConfig>(ConfigAsset))
    {
        TypeInfo.TypeName = TEXT("Clothing");
        TypeInfo.ClassPath = TEXT("/Script/NeoPakTools.NeoClothingPakConfig");
        TypeInfo.bRequiresSkeleton = true;
        TypeInfo.Description = TEXT("Clothing Asset Package");
    }
    else if (Cast<UNeoMapPakConfig>(ConfigAsset))
    {
        TypeInfo.TypeName = TEXT("Map");
        TypeInfo.ClassPath = TEXT("/Script/NeoPakTools.NeoMapPakConfig");
        TypeInfo.bRequiresSkeleton = false;
        TypeInfo.Description = TEXT("Map Asset Package");
    }
    else
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("Unknown DataAsset type: %s"), *ConfigAsset->GetClass()->GetName());
        TypeInfo.TypeName = TEXT("Unknown");
        TypeInfo.ClassPath = ConfigAsset->GetClass()->GetPathName();
        TypeInfo.bRequiresSkeleton = false;
        TypeInfo.Description = TEXT("Unknown Asset Package");
    }

    return TypeInfo;
}

bool FNeoDataAssetTypeRegistry::HasDataAssetTypeInfo(const FString& PakFilePath)
{
    FString TypeInfoPath = GetTypeInfoFilePath(PakFilePath);
    return FPlatformFileManager::Get().GetPlatformFile().FileExists(*TypeInfoPath);
}

TArray<FNeoDataAssetTypeInfo> FNeoDataAssetTypeRegistry::GetAllSupportedTypes()
{
    TArray<FNeoDataAssetTypeInfo> SupportedTypes;

    // 骨骼类型
    FNeoDataAssetTypeInfo SkeletonType;
    SkeletonType.TypeName = TEXT("Skeleton");
    SkeletonType.ClassPath = TEXT("/Script/NeoPakTools.NeoSkeletonPakConfig");
    SkeletonType.bRequiresSkeleton = false;
    SkeletonType.Description = TEXT("Skeleton Asset Package");
    SupportedTypes.Add(SkeletonType);

    // 角色类型
    FNeoDataAssetTypeInfo CharacterType;
    CharacterType.TypeName = TEXT("Character");
    CharacterType.ClassPath = TEXT("/Script/NeoPakTools.NeoCharacterPakConfig");
    CharacterType.bRequiresSkeleton = true;
    CharacterType.Description = TEXT("Character Asset Package");
    SupportedTypes.Add(CharacterType);

    // 动画序列类型
    FNeoDataAssetTypeInfo AnimSequenceType;
    AnimSequenceType.TypeName = TEXT("AnimSequence");
    AnimSequenceType.ClassPath = TEXT("/Script/NeoPakTools.NeoAnimSequencePakConfig");
    AnimSequenceType.bRequiresSkeleton = true;
    AnimSequenceType.Description = TEXT("Animation Sequence Package");
    SupportedTypes.Add(AnimSequenceType);

    // 动画蒙太奇类型
    FNeoDataAssetTypeInfo AnimMontageType;
    AnimMontageType.TypeName = TEXT("AnimMontage");
    AnimMontageType.ClassPath = TEXT("/Script/NeoPakTools.NeoAnimMontagePakConfig");
    AnimMontageType.bRequiresSkeleton = true;
    AnimMontageType.Description = TEXT("Animation Montage Package");
    SupportedTypes.Add(AnimMontageType);

    // 服装类型
    FNeoDataAssetTypeInfo ClothingType;
    ClothingType.TypeName = TEXT("Clothing");
    ClothingType.ClassPath = TEXT("/Script/NeoPakTools.NeoClothingPakConfig");
    ClothingType.bRequiresSkeleton = true;
    ClothingType.Description = TEXT("Clothing Asset Package");
    SupportedTypes.Add(ClothingType);

    // 地图类型
    FNeoDataAssetTypeInfo MapType;
    MapType.TypeName = TEXT("Map");
    MapType.ClassPath = TEXT("/Script/NeoPakTools.NeoMapPakConfig");
    MapType.bRequiresSkeleton = false;
    MapType.Description = TEXT("Map Asset Package");
    SupportedTypes.Add(MapType);

    return SupportedTypes;
}

FString FNeoDataAssetTypeRegistry::SerializeTypeInfo(const FNeoDataAssetTypeInfo& TypeInfo)
{
    // 使用JSON格式序列化
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("TypeName"), TypeInfo.TypeName);
    JsonObject->SetStringField(TEXT("ClassPath"), TypeInfo.ClassPath);
    JsonObject->SetBoolField(TEXT("RequiresSkeleton"), TypeInfo.bRequiresSkeleton);
    JsonObject->SetStringField(TEXT("Description"), TypeInfo.Description);

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}

FNeoDataAssetTypeInfo FNeoDataAssetTypeRegistry::DeserializeTypeInfo(const FString& SerializedData)
{
    FNeoDataAssetTypeInfo TypeInfo;

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(SerializedData);

    if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
    {
        TypeInfo.TypeName = JsonObject->GetStringField(TEXT("TypeName"));
        TypeInfo.ClassPath = JsonObject->GetStringField(TEXT("ClassPath"));
        TypeInfo.bRequiresSkeleton = JsonObject->GetBoolField(TEXT("RequiresSkeleton"));
        TypeInfo.Description = JsonObject->GetStringField(TEXT("Description"));
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to deserialize DataAsset type info from JSON"));
    }

    return TypeInfo;
}

FString FNeoDataAssetTypeRegistry::GetTypeInfoFilePath(const FString& PakFilePath)
{
    // 获取PAK文件的目录
    FString PakDirectory = FPaths::GetPath(PakFilePath);
    FString PakBaseName = FPaths::GetBaseFilename(PakFilePath);

    // 创建类型信息文件路径（与PAK文件同目录）
    return FPaths::Combine(PakDirectory, PakBaseName + TEXT("_") + DataAssetTypeInfoFileName);
}
