// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoSkeletonPakConfig.h"
#include "NeoPakTools.h"

DEFINE_LOG_CATEGORY_STATIC(LogNeoSkeletonPakConfig, Log, All);

#if WITH_EDITOR
bool UNeoSkeletonPakConfig::ValidateConfiguration()
{
    // 检查是否指定了骨骼资产（使用更可靠的方法）
    if (SkeletonAsset.IsNull())
    {
        UE_LOG(LogNeoSkeletonPakConfig, Error, TEXT("No skeleton asset specified"));
        return false;
    }

    // 检查骨骼资产路径是否有效
    FString SkeletonPath = SkeletonAsset.GetLongPackageName();
    if (SkeletonPath.IsEmpty())
    {
        UE_LOG(LogNeoSkeletonPakConfig, Error, TEXT("Skeleton asset path is empty"));
        return false;
    }
    
    // 检查输出文件名
    if (OutputPakFileName.IsEmpty())
    {
        UE_LOG(LogNeoSkeletonPakConfig, Error, TEXT("Output PAK file name is empty"));
        return false;
    }
    
    return true;
}

bool UNeoSkeletonPakConfig::ExecutePackaging()
{
    // 首先验证配置
    if (!ValidateConfiguration())
    {
        UE_LOG(LogNeoSkeletonPakConfig, Error, TEXT("Configuration validation failed for: %s"), *ConfigName);
        return false;
    }
    
    // TODO: 实现实际的打包逻辑
    UE_LOG(LogNeoSkeletonPakConfig, Log, TEXT("Executing packaging for skeleton config: %s"), *ConfigName);
    UE_LOG(LogNeoSkeletonPakConfig, Log, TEXT("Skeleton Asset: %s"), *SkeletonAsset.ToString());
    UE_LOG(LogNeoSkeletonPakConfig, Log, TEXT("Output Path: %s"), *GetFullOutputPath());
    
    return true;
}

TArray<FSoftObjectPath> UNeoSkeletonPakConfig::GetAssetsToPackage() const
{
    TArray<FSoftObjectPath> Assets;
    
    if (SkeletonAsset.IsValid())
    {
        Assets.Add(SkeletonAsset.ToSoftObjectPath());
    }
    
    return Assets;
}
#endif
