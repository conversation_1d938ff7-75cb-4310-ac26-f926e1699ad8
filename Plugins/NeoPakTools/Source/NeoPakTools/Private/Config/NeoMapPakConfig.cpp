// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoMapPakConfig.h"
#include "NeoPakTools.h"
#include "Misc/PackageName.h"

DEFINE_LOG_CATEGORY_STATIC(LogNeoMapPakConfig, Log, All);

#if WITH_EDITOR
bool UNeoMapPakConfig::ValidateConfiguration()
{
    // 检查是否有地图资产
    if (!Map.IsValid())
    {
        UE_LOG(LogNeoMapPakConfig, Error, TEXT("No map specified"));
        return false;
    }
    
    // 检查地图文件是否存在
    FString MapPath = Map.GetLongPackageName();
    if (!FPackageName::DoesPackageExist(MapPath))
    {
        UE_LOG(LogNeoMapPakConfig, Error, TEXT("Map package does not exist: %s"), *MapPath);
        return false;
    }
    
    // 检查输出文件名
    if (OutputPakFileName.IsEmpty())
    {
        UE_LOG(LogNeoMapPakConfig, Error, TEXT("Output PAK file name is empty"));
        return false;
    }
    
    return true;
}

bool UNeoMapPakConfig::ExecutePackaging()
{
    // 首先验证配置
    if (!ValidateConfiguration())
    {
        UE_LOG(LogNeoMapPakConfig, Error, TEXT("Configuration validation failed for: %s"), *ConfigName);
        return false;
    }
    
    // TODO: 实现实际的打包逻辑
    UE_LOG(LogNeoMapPakConfig, Log, TEXT("Executing packaging for map config: %s"), *ConfigName);
    UE_LOG(LogNeoMapPakConfig, Log, TEXT("Map: %s"), *Map.ToString());
    UE_LOG(LogNeoMapPakConfig, Log, TEXT("Output Path: %s"), *GetFullOutputPath());
    
    return true;
}

TArray<FSoftObjectPath> UNeoMapPakConfig::GetAssetsToPackage() const
{
    TArray<FSoftObjectPath> Assets;
    
    if (Map.IsValid())
    {
        Assets.Add(Map.ToSoftObjectPath());
    }
    
    return Assets;
}
#endif
