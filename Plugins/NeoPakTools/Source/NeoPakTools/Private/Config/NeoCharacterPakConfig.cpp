// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoCharacterPakConfig.h"
#include "NeoPakTools.h"

DEFINE_LOG_CATEGORY_STATIC(LogNeoCharacterPakConfig, Log, All);

#if WITH_EDITOR
bool UNeoCharacterPakConfig::ValidateConfiguration()
{
    // 检查依赖的骨骼资产DataAsset（使用更可靠的方法）
    if (RequiredSkeletonConfig.IsNull())
    {
        UE_LOG(LogNeoCharacterPakConfig, Error, TEXT("Required skeleton config is not specified"));
        return false;
    }

    // 检查骨骼配置路径是否有效
    FString SkeletonConfigPath = RequiredSkeletonConfig.GetLongPackageName();
    if (SkeletonConfigPath.IsEmpty())
    {
        UE_LOG(LogNeoCharacterPakConfig, Error, TEXT("Required skeleton config path is empty"));
        return false;
    }

    // 检查是否有角色蓝图（使用更可靠的方法）
    if (CharacterBlueprint.IsNull())
    {
        UE_LOG(LogNeoCharacterPakConfig, Error, TEXT("No character blueprint specified"));
        return false;
    }

    // 检查角色蓝图路径是否有效
    FString CharacterBlueprintPath = CharacterBlueprint.GetLongPackageName();
    if (CharacterBlueprintPath.IsEmpty())
    {
        UE_LOG(LogNeoCharacterPakConfig, Error, TEXT("Character blueprint path is empty"));
        return false;
    }
    
    // 检查输出文件名
    if (OutputPakFileName.IsEmpty())
    {
        UE_LOG(LogNeoCharacterPakConfig, Error, TEXT("Output PAK file name is empty"));
        return false;
    }
    
    return true;
}

bool UNeoCharacterPakConfig::ExecutePackaging()
{
    // 首先验证配置
    if (!ValidateConfiguration())
    {
        UE_LOG(LogNeoCharacterPakConfig, Error, TEXT("Configuration validation failed for: %s"), *ConfigName);
        return false;
    }
    
    // TODO: 实现实际的打包逻辑
    UE_LOG(LogNeoCharacterPakConfig, Log, TEXT("Executing packaging for character config: %s"), *ConfigName);
    UE_LOG(LogNeoCharacterPakConfig, Log, TEXT("Character Blueprint: %s"), *CharacterBlueprint.ToString());
    UE_LOG(LogNeoCharacterPakConfig, Log, TEXT("Required Skeleton Config: %s"), *RequiredSkeletonConfig.ToString());
    UE_LOG(LogNeoCharacterPakConfig, Log, TEXT("Output Path: %s"), *GetFullOutputPath());
    
    return true;
}

TArray<FSoftObjectPath> UNeoCharacterPakConfig::GetAssetsToPackage() const
{
    TArray<FSoftObjectPath> Assets;
    
    if (CharacterBlueprint.IsValid())
    {
        Assets.Add(CharacterBlueprint.ToSoftObjectPath());
    }
    
    return Assets;
}
#endif
