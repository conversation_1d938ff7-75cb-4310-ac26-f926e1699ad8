// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoPakConfigAssetBase.h"
#include "Config/NeoPakToolsSettings.h"
#include "Misc/PackageName.h"

FString UNeoPakConfigAssetBase::GetFullOutputPath() const
{
    const UNeoPakToolsSettings* Settings = UNeoPakToolsSettings::GetNeoPakToolsSettings();
    if (Settings)
    {
        return Settings->GetFullOutputPath(OutputPakFileName);
    }

    return OutputPakFileName;
}

void UNeoPakConfigAssetBase::UpdateOutputPakFileName()
{
    FString AssetPath = GetAssetPackagePath();
    if (!AssetPath.IsEmpty())
    {
        // 将路径中的斜杠替换为下划线，创建唯一的PAK文件名
        FString SafeFileName = AssetPath;
        SafeFileName = SafeFileName.Replace(TEXT("/"), TEXT("_"));
        SafeFileName = SafeFileName.Replace(TEXT("\\"), TEXT("_"));

        // 移除开头的下划线（如果有的话）
        if (SafeFileName.StartsWith(TEXT("_")))
        {
            SafeFileName = SafeFileName.RightChop(1);
        }

        OutputPakFileName = SafeFileName + TEXT(".pak");
    }
    else
    {
        // 如果无法获取包路径，使用资产名称作为后备
        OutputPakFileName = GetName() + TEXT(".pak");
    }
}

FString UNeoPakConfigAssetBase::GetAssetPackagePath() const
{
    if (UPackage* Package = GetPackage())
    {
        FString PackageName = Package->GetName();

        // 移除包名前缀（如 /Game/, /Content/ 等）
        FString AssetPath;
        if (PackageName.StartsWith(TEXT("/Game/")))
        {
            AssetPath = PackageName.RightChop(6); // 移除 "/Game/"
        }
        else if (PackageName.StartsWith(TEXT("/Content/")))
        {
            AssetPath = PackageName.RightChop(9); // 移除 "/Content/"
        }
        else
        {
            // 对于其他路径，直接使用包名
            AssetPath = PackageName;
        }

        return AssetPath;
    }

    return FString();
}

void UNeoPakConfigAssetBase::PostLoad()
{
    Super::PostLoad();

    // 加载后确保PAK文件名是最新的
    UpdateOutputPakFileName();
}

void UNeoPakConfigAssetBase::PostInitProperties()
{
    Super::PostInitProperties();

    // 初始化时设置默认值
    if (ConfigName.IsEmpty())
    {
        ConfigName = GetName();
    }

    // 初始化PAK文件名
    UpdateOutputPakFileName();
}

#if WITH_EDITOR
void UNeoPakConfigAssetBase::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

    // 当属性改变时，自动更新PAK文件名
    if (PropertyChangedEvent.Property)
    {
        FName PropertyName = PropertyChangedEvent.Property->GetFName();

        // 如果配置名称为空，使用资产名称
        if (PropertyName == GET_MEMBER_NAME_CHECKED(UNeoPakConfigAssetBase, ConfigName))
        {
            if (ConfigName.IsEmpty())
            {
                ConfigName = GetName();
            }
        }
    }

    // 无论什么属性改变，都更新PAK文件名（因为资产可能被重命名）
    UpdateOutputPakFileName();
}
#endif
