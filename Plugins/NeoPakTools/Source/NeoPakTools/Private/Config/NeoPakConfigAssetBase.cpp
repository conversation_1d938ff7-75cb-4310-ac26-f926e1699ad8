// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoPakConfigAssetBase.h"
#include "Config/NeoPakToolsSettings.h"

FString UNeoPakConfigAssetBase::GetFullOutputPath() const
{
    const UNeoPakToolsSettings* Settings = UNeoPakToolsSettings::GetNeoPakToolsSettings();
    if (Settings)
    {
        return Settings->GetFullOutputPath(OutputPakFileName);
    }

    return OutputPakFileName;
}

#if WITH_EDITOR
void UNeoPakConfigAssetBase::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);
    
    // 当属性改变时，可以在这里添加验证逻辑
    if (PropertyChangedEvent.Property)
    {
        FName PropertyName = PropertyChangedEvent.Property->GetFName();
        
        // 如果配置名称为空，使用资产名称
        if (PropertyName == GET_MEMBER_NAME_CHECKED(UNeoPakConfigAssetBase, ConfigName))
        {
            if (ConfigName.IsEmpty())
            {
                ConfigName = GetName();
            }
        }
        
        // 如果输出文件名为空，生成默认名称
        if (PropertyName == GET_MEMBER_NAME_CHECKED(UNeoPakConfigAssetBase, OutputPakFileName))
        {
            if (OutputPakFileName.IsEmpty())
            {
                OutputPakFileName = GetName() + TEXT(".pak");
            }
        }
    }
}
#endif
