// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "Utils/NeoAssetDependencyResolver.h"
#include "NeoPakManager.generated.h"

// PAK加载状态
UENUM(BlueprintType)
enum class EPakLoadStatus : uint8
{
    NotLoaded       UMETA(DisplayName = "Not Loaded"),
    Loading         UMETA(DisplayName = "Loading"),
    Loaded          UMETA(DisplayName = "Loaded"),
    Failed          UMETA(DisplayName = "Failed")
};

// PAK条目信息
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakEntry
{
    GENERATED_BODY()

    // PAK文件路径
    UPROPERTY(BlueprintReadOnly)
    FString PakFilePath;

    // 加载状态
    UPROPERTY(BlueprintReadOnly)
    EPakLoadStatus LoadStatus = EPakLoadStatus::NotLoaded;

    // 包含的资产列表
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> ContainedAssets;

    // 加载时间
    UPROPERTY(BlueprintReadOnly)
    FDateTime LoadTime;
};

UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoPakManager : public UObject
{
    GENERATED_BODY()

public:
    // 打包功能（通过DataAsset配置）
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool PackageFromConfig(UNeoPakConfigAssetBase* ConfigAsset);

    // 带依赖检查的打包功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool PackageFromConfigWithDependencyCheck(UNeoPakConfigAssetBase* ConfigAsset, bool bAutoFixDependencies = false);

    // 运行时加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool LoadPakFile(const FString& PakFilePath);

    // 卸载PAK
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool UnloadPakFile(const FString& PakFilePath);

    // 依赖检查
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetMissingDependencies(const FString& PakFilePath);

    // 获取已加载的PAK列表
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FNeoPakEntry> GetLoadedPaks() const;

    // 获取PAK加载状态
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    EPakLoadStatus GetPakLoadStatus(const FString& PakFilePath) const;

    // 获取单例实例
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static UNeoPakManager* GetInstance();

private:
    // 已加载的PAK文件映射
    UPROPERTY()
    TMap<FString, FNeoPakEntry> LoadedPaks;

    // 依赖解析器
    TSharedPtr<FNeoAssetDependencyResolver> DependencyResolver;

    // 单例实例
    static UNeoPakManager* Instance;

    // 执行实际的打包操作
    bool ExecutePackaging(UNeoPakConfigAssetBase* ConfigAsset);

    // 创建PAK文件
    bool CreatePakFile(const TArray<FString>& AssetPaths, const FString& OutputPath);

    // 验证PAK文件
    bool ValidatePakFile(const FString& PakFilePath);
};
