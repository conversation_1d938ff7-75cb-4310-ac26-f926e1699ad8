// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "Config/NeoSkeletonPakConfig.h"
#include "Animation/AnimMontage.h"
#include "NeoAnimMontagePakConfig.generated.h"

// 动画蒙太奇资产配置
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoAnimMontagePakConfig : public UNeoPakConfigAssetBase
{
    GENERATED_BODY()

public:
    // 依赖的骨骼资产DataAsset
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dependencies", meta = (AllowedClasses = "NeoSkeletonPakConfig"))
    TSoftObjectPtr<UNeoSkeletonPakConfig> RequiredSkeletonConfig;
    
    // 要打包的动画蒙太奇
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Montage")
    TSoftObjectPtr<UAnimMontage> AnimationMontage;

#if WITH_EDITOR
    virtual bool ValidateConfiguration() override;
    virtual bool ExecutePackaging() override;
    virtual TArray<FSoftObjectPath> GetAssetsToPackage() const override;

private:
    // 验证动画蒙太奇与骨骼的兼容性
    bool IsAnimMontageCompatibleWithSkeletonConfig(const TSoftObjectPtr<UAnimMontage>& AnimMontage, const TSoftObjectPtr<UNeoSkeletonPakConfig>& SkeletonConfig) const;
#endif
};
