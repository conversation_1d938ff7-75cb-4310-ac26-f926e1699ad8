// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "Config/NeoSkeletonPakConfig.h"
#include "Animation/AnimSequence.h"
#include "NeoAnimSequencePakConfig.generated.h"

// 动画序列资产配置
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoAnimSequencePakConfig : public UNeoPakConfigAssetBase
{
    GENERATED_BODY()

public:
    // 依赖的骨骼资产DataAsset
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dependencies", meta = (AllowedClasses = "NeoSkeletonPakConfig"))
    TSoftObjectPtr<UNeoSkeletonPakConfig> RequiredSkeletonConfig;
    
    // 要打包的动画序列
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Sequence")
    TSoftObjectPtr<UAnimSequence> AnimationSequence;

#if WITH_EDITOR
    virtual bool ValidateConfiguration() override;
    virtual bool ExecutePackaging() override;
    virtual TArray<FSoftObjectPath> GetAssetsToPackage() const override;

private:
    // 验证动画序列与骨骼的兼容性
    bool IsAnimationCompatibleWithSkeletonConfig(const TSoftObjectPtr<UAnimSequence>& AnimSeq, const TSoftObjectPtr<UNeoSkeletonPakConfig>& SkeletonConfig) const;
#endif
};
