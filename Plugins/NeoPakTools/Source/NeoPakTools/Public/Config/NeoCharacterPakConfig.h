// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "Config/NeoSkeletonPakConfig.h"
#include "Engine/Blueprint.h"
#include "NeoCharacterPakConfig.generated.h"

// 角色资产配置
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoCharacterPakConfig : public UNeoPakConfigAssetBase
{
    GENERATED_BODY()

public:
    // 依赖的骨骼资产DataAsset
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dependencies", meta = (AllowedClasses = "NeoSkeletonPakConfig"))
    TSoftObjectPtr<UNeoSkeletonPakConfig> RequiredSkeletonConfig;
    
    // 要打包的角色蓝图
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Character Asset")
    TSoftObjectPtr<UBlueprint> CharacterBlueprint;

#if WITH_EDITOR
    virtual bool ValidateConfiguration() override;
    virtual bool ExecutePackaging() override;
    virtual TArray<FSoftObjectPath> GetAssetsToPackage() const override;
#endif
};
