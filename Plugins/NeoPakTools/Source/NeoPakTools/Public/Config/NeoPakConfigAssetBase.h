// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "Engine/EngineTypes.h"
#include "UObject/SoftObjectPath.h"
#include "NeoPakConfigAssetBase.generated.h"

// 基础打包配置DataAsset（简化版）
UCLASS(BlueprintType, Abstract)
class NEOPAKTOOLS_API UNeoPakConfigAssetBase : public UDataAsset
{
    GENERATED_BODY()

public:
    // 配置名称
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic")
    FString ConfigName;
    
    // 输出PAK文件名（不包含路径，使用插件配置的输出目录）
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic")
    FString OutputPakFileName;

    // 获取完整输出路径（使用插件配置）
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    FString GetFullOutputPath() const;

#if WITH_EDITOR
    // 纯虚函数：验证配置有效性（子类实现具体逻辑）
    UFUNCTION(CallInEditor, Category = "Validation")
    virtual bool ValidateConfiguration() PURE_VIRTUAL(UNeoPakConfigAssetBase::ValidateConfiguration, return false;);

    // 纯虚函数：执行打包（子类实现具体逻辑）
    UFUNCTION(CallInEditor, Category = "Packaging")
    virtual bool ExecutePackaging() PURE_VIRTUAL(UNeoPakConfigAssetBase::ExecutePackaging, return false;);

    // 获取要打包的资产列表
    virtual TArray<FSoftObjectPath> GetAssetsToPackage() const PURE_VIRTUAL(UNeoPakConfigAssetBase::GetAssetsToPackage, return TArray<FSoftObjectPath>(););

    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif
};
