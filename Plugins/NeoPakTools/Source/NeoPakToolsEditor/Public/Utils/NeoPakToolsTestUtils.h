// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"

/**
 * Utility class for testing NeoPakTools functionality
 */
class NEOPAKTOOLSEDITOR_API FNeoPakToolsTestUtils
{
public:
    /** Create sample DataAsset configurations for testing */
    static void CreateSampleConfigurations();

    /** Test PAK file creation with sample data */
    static bool TestPakCreation();

    /** Validate all plugin components */
    static void ValidatePluginComponents();

    /** Create a sample skeleton configuration */
    static class UNeoSkeletonPakConfig* CreateSampleSkeletonConfig();

    /** Create a sample character configuration */
    static class UNeoCharacterPakConfig* CreateSampleCharacterConfig(class UNeoSkeletonPakConfig* SkeletonConfig);

    /** Create a sample map configuration */
    static class UNeoMapPakConfig* CreateSampleMapConfig();

    /** Get or create a test directory */
    static FString GetTestDirectory();

    /** Clean up test files */
    static void CleanupTestFiles();

    /** Log test results */
    static void LogTestResult(const FString& TestName, bool bSuccess, const FString& Details = TEXT(""));

private:
    /** Test directory path */
    static FString TestDirectoryPath;
};
