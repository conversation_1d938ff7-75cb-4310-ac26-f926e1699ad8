// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "AssetTools/NeoPakConfigActionsBase.h"
#include "Config/NeoSkeletonPakConfig.h"

class FNeoSkeletonPakConfigActions : public FNeoPakConfigActionsBase
{
public:
    // FAssetTypeActions_Base interface
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual UClass* GetSupportedClass() const override;
    virtual uint32 GetCategories() override;
    virtual void GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder) override;
    virtual bool HasActions(const TArray<UObject*>& InObjects) const override { return true; }

private:
    // Menu actions
    void ExecutePackaging(TArray<TWeakObjectPtr<UNeoSkeletonPakConfig>> Objects);
    void ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoSkeletonPakConfig>> Objects);
    void ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoSkeletonPakConfig>> Objects);
    
    // Can execute checks
    bool CanExecutePackaging(TArray<TWeakObjectPtr<UNeoSkeletonPakConfig>> Objects) const;
};
