// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "Config/NeoAnimSequencePakConfig.h"

class NEOPAKTOOLSEDITOR_API FNeoAnimSequencePakConfigActions : public FAssetTypeActions_Base
{
public:
    // IAssetTypeActions Implementation
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual UClass* GetSupportedClass() const override;
    virtual uint32 GetCategories() override;
    virtual void GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder) override;

private:
    // Menu actions
    void ExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> Objects);
    void ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> Objects);
    void ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> Objects);
    
    // Can execute checks
    bool CanExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> Objects) const;
};
