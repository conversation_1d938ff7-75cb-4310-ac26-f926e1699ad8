// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "Config/NeoAnimMontagePakConfig.h"

class NEOPAKTOOLSEDITOR_API FNeoAnimMontagePakConfigActions : public FAssetTypeActions_Base
{
public:
    // IAssetTypeActions Implementation
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual UClass* GetSupportedClass() const override;
    virtual uint32 GetCategories() override;
    virtual void GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder) override;

private:
    // Menu actions
    void ExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects);
    void ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects);
    void ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects);
    
    // Can execute checks
    bool CanExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects) const;
};
