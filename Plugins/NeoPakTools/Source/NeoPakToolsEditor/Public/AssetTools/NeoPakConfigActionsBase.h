// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "Config/NeoPakConfigAssetBase.h"

/**
 * Base class for all NeoPakConfig asset type actions
 * Provides common functionality for all DataAsset configuration types
 */
class NEOPAKTOOLSEDITOR_API FNeoPakConfigActionsBase : public FAssetTypeActions_Base
{
public:
    // FAssetTypeActions_Base interface
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual uint32 GetCategories() override;
    virtual void GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder) override;
    virtual bool HasActions(const TArray<UObject*>& InObjects) const override { return true; }

protected:
    // 执行打包操作
    void ExecutePackaging(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects);

    // 验证配置
    void ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects);

    // 手动检查目录依赖
    void ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects);

    // 批量打包
    void ExecuteBatchPackaging(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects);

    // 显示PAK信息
    void ExecuteShowPakInfo(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects);

    // 打开配置编辑器
    void ExecuteOpenConfigEditor(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects);

    // 检查是否可以执行打包
    bool CanExecutePackaging(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects) const;

    // 检查是否可以显示PAK信息
    bool CanShowPakInfo(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects) const;

    // 获取资产类型特定的颜色（子类实现）
    virtual FColor GetAssetTypeColor() const = 0;

    // 获取资产类型名称（子类实现）
    virtual FText GetAssetTypeName() const = 0;

private:
    // 显示操作结果对话框
    void ShowResultDialog(const FText& Title, const FText& Message, bool bSuccess);

    // 获取有效的配置对象
    TArray<UNeoPakConfigAssetBase*> GetValidConfigs(const TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>>& Objects) const;
};
