// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "PakManager/NeoPakManager.h"

class SListView;
class STextBlock;
class SButton;
class SProgressBar;
class SNeoPakInfoWidget;

/**
 * Main PAK management window for NeoPakTools
 */
class NEOPAKTOOLSEDITOR_API SNeoPakManagerWindow : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(SNeoPakManagerWindow) {}
    SLATE_END_ARGS()

    /** Constructs this widget with InArgs */
    void Construct(const FArguments& InArgs);

    /** Refresh all content */
    void RefreshAll();

private:
    /** PAK Manager instance */
    TWeakObjectPtr<UNeoPakManager> PakManager;

    /** List of DataAsset configurations */
    TArray<TSharedPtr<UNeoPakConfigAssetBase*>> ConfigAssets;

    /** List of PAK files */
    TArray<TSharedPtr<FNeoPakEntry>> PakEntries;

    /** Selected configuration asset */
    TSharedPtr<UNeoPakConfigAssetBase*> SelectedConfig;

    /** Selected PAK entry */
    TSharedPtr<FNeoPakEntry> SelectedPakEntry;

    // UI Components
    TSharedPtr<SListView<TSharedPtr<UNeoPakConfigAssetBase*>>> ConfigListView;
    TSharedPtr<SListView<TSharedPtr<FNeoPakEntry>>> PakListView;
    TSharedPtr<SNeoPakInfoWidget> PakInfoWidget;
    TSharedPtr<STextBlock> StatusText;
    TSharedPtr<SProgressBar> ProgressBar;

    // Buttons
    TSharedPtr<SButton> PackageSelectedButton;
    TSharedPtr<SButton> PackageAllButton;
    TSharedPtr<SButton> ValidateAllButton;
    TSharedPtr<SButton> RefreshButton;
    TSharedPtr<SButton> CleanOutputButton;

    /** Create the main toolbar */
    TSharedRef<SWidget> CreateToolbar();

    /** Create the configuration list panel */
    TSharedRef<SWidget> CreateConfigListPanel();

    /** Create the PAK list panel */
    TSharedRef<SWidget> CreatePakListPanel();

    /** Create the info panel */
    TSharedRef<SWidget> CreateInfoPanel();

    /** Create the status bar */
    TSharedRef<SWidget> CreateStatusBar();

    // List view callbacks
    TSharedRef<ITableRow> OnGenerateConfigRow(TSharedPtr<UNeoPakConfigAssetBase*> Item, const TSharedRef<STableViewBase>& OwnerTable);
    TSharedRef<ITableRow> OnGeneratePakRow(TSharedPtr<FNeoPakEntry> Item, const TSharedRef<STableViewBase>& OwnerTable);
    void OnConfigSelectionChanged(TSharedPtr<UNeoPakConfigAssetBase*> SelectedItem, ESelectInfo::Type SelectInfo);
    void OnPakSelectionChanged(TSharedPtr<FNeoPakEntry> SelectedItem, ESelectInfo::Type SelectInfo);

    // Button callbacks
    FReply OnPackageSelectedClicked();
    FReply OnPackageAllClicked();
    FReply OnValidateAllClicked();
    FReply OnRefreshClicked();
    FReply OnCleanOutputClicked();

    // Helper functions
    void RefreshConfigList();
    void RefreshPakList();
    void UpdateButtonStates();
    void UpdateStatusText(const FText& NewStatus);
    void ShowProgressBar(bool bShow);

    // Can execute checks
    bool CanPackageSelected() const;
    bool CanPackageAll() const;
    bool CanValidateAll() const;
    bool CanCleanOutput() const;

    // Get display text
    FText GetSelectedConfigText() const;
    FText GetSelectedPakText() const;
    FText GetStatusText() const;
    TOptional<float> GetProgressPercent() const;
};
