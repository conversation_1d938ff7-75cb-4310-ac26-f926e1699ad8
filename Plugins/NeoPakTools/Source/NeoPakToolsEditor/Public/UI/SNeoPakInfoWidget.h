// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Utils/NeoDataAssetTypeRegistry.h"

class STextBlock;
class SVerticalBox;

/**
 * Widget to display PAK file information and DataAsset type details
 */
class NEOPAKTOOLSEDITOR_API SNeoPakInfoWidget : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(SNeoPakInfoWidget) {}
        SLATE_ARGUMENT(FString, PakFilePath)
    SLATE_END_ARGS()

    /** Constructs this widget with InArgs */
    void Construct(const FArguments& InArgs);

    /** Refresh the widget content */
    void RefreshContent();

    /** Set the PAK file path to display */
    void SetPakFilePath(const FString& InPakFilePath);

private:
    /** The PAK file path being displayed */
    FString PakFilePath;

    /** Main content container */
    TSharedPtr<SVerticalBox> MainContainer;

    /** Text block for PAK file path */
    TSharedPtr<STextBlock> PakFilePathText;

    /** Text block for DataAsset type info */
    TSharedPtr<STextBlock> DataAssetTypeText;

    /** Text block for type description */
    TSharedPtr<STextBlock> TypeDescriptionText;

    /** Text block for skeleton requirement */
    TSharedPtr<STextBlock> SkeletonRequirementText;

    /** Text block for file status */
    TSharedPtr<STextBlock> FileStatusText;

    /** Create the main content */
    TSharedRef<SWidget> CreateMainContent();

    /** Update the displayed information */
    void UpdateDisplayedInfo();

    /** Get the PAK file status text */
    FText GetFileStatusText() const;

    /** Get the PAK file status color */
    FSlateColor GetFileStatusColor() const;

    /** Get the DataAsset type info text */
    FText GetDataAssetTypeText() const;

    /** Get the type description text */
    FText GetTypeDescriptionText() const;

    /** Get the skeleton requirement text */
    FText GetSkeletonRequirementText() const;
};
