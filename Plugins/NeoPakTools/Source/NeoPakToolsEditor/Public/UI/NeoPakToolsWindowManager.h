// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Framework/Docking/TabManager.h"

class SNeoPakManagerWindow;

/**
 * Manager for NeoPakTools editor windows
 */
class NEOPAKTOOLSEDITOR_API FNeoPakToolsWindowManager
{
public:
    /** Initialize the window manager */
    static void Initialize();

    /** Shutdown the window manager */
    static void Shutdown();

    /** Open the main PAK manager window */
    static void OpenPakManagerWindow();

    /** Close the main PAK manager window */
    static void ClosePakManagerWindow();

    /** Check if the PAK manager window is open */
    static bool IsPakManagerWindowOpen();

private:
    /** Tab spawner for the PAK manager window */
    static TSharedRef<SDockTab> SpawnPakManagerTab(const FSpawnTabArgs& Args);

    /** Tab ID for the PAK manager window */
    static const FName PakManagerTabId;

    /** Reference to the PAK manager window widget */
    static TSharedPtr<SNeoPakManagerWindow> PakManagerWindow;

    /** Flag to track if the window manager is initialized */
    static bool bIsInitialized;
};
