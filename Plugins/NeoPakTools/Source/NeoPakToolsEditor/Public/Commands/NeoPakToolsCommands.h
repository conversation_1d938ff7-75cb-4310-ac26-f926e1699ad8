// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "EditorStyleSet.h"

class FNeoPakToolsCommands : public TCommands<FNeoPakToolsCommands>
{
public:
    FNeoPakToolsCommands()
        : TCommands<FNeoPakToolsCommands>(TEXT("NeoPakTools"), NSLOCTEXT("Contexts", "NeoPakTools", "NeoPakTools Plugin"), NAME_None, FEditorStyle::GetStyleSetName())
    {
    }

    // TCommands<> interface
    virtual void RegisterCommands() override;

public:
    // 打开PAK工具窗口
    TSharedPtr<FUICommandInfo> OpenPakToolsWindow;

    // 批量打包所有配置
    TSharedPtr<FUICommandInfo> BatchPackageAll;

    // 验证所有配置
    TSharedPtr<FUICommandInfo> ValidateAllConfigs;

    // 清理输出目录
    TSharedPtr<FUICommandInfo> CleanOutputDirectory;

    // 显示PAK信息
    TSharedPtr<FUICommandInfo> ShowPakInfo;

    // 刷新PAK列表
    TSharedPtr<FUICommandInfo> RefreshPakList;
};
