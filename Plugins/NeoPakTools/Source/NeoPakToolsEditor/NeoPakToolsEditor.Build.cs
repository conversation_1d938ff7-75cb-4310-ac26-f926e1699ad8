// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class NeoPakToolsEditor : ModuleRules
{
	public NeoPakToolsEditor(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		
		PublicIncludePaths.AddRange(
			new string[] {
			}
			);
				
		
		PrivateIncludePaths.AddRange(
			new string[] {
			}
			);
			
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"CoreUObject",
				"Engine",
				"NeoPakTools",
				"UnrealEd",
				"ToolMenus",
				"AssetTools",
				"ContentBrowser"
			}
			);
			
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"Slate",
				"SlateCore",
				"EditorWidgets",
				"PropertyEditor",
				"ContentBrowser",
				"AssetRegistry",
				"AssetTools",
				"ToolMenus",
				"Json"
			}
			);
		
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
			}
			);
	}
}
