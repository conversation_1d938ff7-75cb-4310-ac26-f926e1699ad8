// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoSkeletonPakConfigActions.h"

#define LOCTEXT_NAMESPACE "NeoSkeletonPakConfigActions"

UClass* FNeoSkeletonPakConfigActions::GetSupportedClass() const
{
    return UNeoSkeletonPakConfig::StaticClass();
}

FColor FNeoSkeletonPakConfigActions::GetAssetTypeColor() const
{
    return FColor(255, 196, 128); // Orange
}

FText FNeoSkeletonPakConfigActions::GetAssetTypeName() const
{
    return LOCTEXT("AssetTypeActions_NeoSkeletonPakConfig", "Skeleton Pack Config");
}

void FNeoSkeletonPakConfigActions::GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder)
{
    auto SkeletonConfigs = GetTypedWeakObjectPtrs<UNeoSkeletonPakConfig>(InObjects);

    MenuBuilder.AddMenuEntry(
        LOCTEXT("SkeletonPakConfig_ExecutePackaging", "Execute Packaging"),
        LOCTEXT("SkeletonPakConfig_ExecutePackagingTooltip", "Execute packaging for this skeleton configuration"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoSkeletonPakConfigActions::ExecutePackaging, SkeletonConfigs),
            FCanExecuteAction::CreateSP(this, &FNeoSkeletonPakConfigActions::CanExecutePackaging, SkeletonConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("SkeletonPakConfig_ValidateConfig", "Validate Configuration"),
        LOCTEXT("SkeletonPakConfig_ValidateConfigTooltip", "Validate the configuration settings"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoSkeletonPakConfigActions::ExecuteValidateConfig, SkeletonConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("SkeletonPakConfig_CheckDependencies", "Check Directory Dependencies"),
        LOCTEXT("SkeletonPakConfig_CheckDependenciesTooltip", "Check if all dependencies are in the correct directory"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoSkeletonPakConfigActions::ExecuteCheckDirectoryDependencies, SkeletonConfigs)
        )
    );
}

void FNeoSkeletonPakConfigActions::ExecutePackaging(TArray<TWeakObjectPtr<UNeoSkeletonPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoSkeletonPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Executing packaging for skeleton config: %s"), *Config->GetName());
            
            if (Config->ExecutePackaging())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingSuccess", "Successfully packaged skeleton config: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingFailed", "Failed to package skeleton config: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoSkeletonPakConfigActions::ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoSkeletonPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoSkeletonPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating skeleton config: %s"), *Config->GetName());
            
            if (Config->ValidateConfiguration())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationSuccess", "Configuration is valid: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationFailed", "Configuration validation failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoSkeletonPakConfigActions::ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoSkeletonPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoSkeletonPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking directory dependencies for skeleton config: %s"), *Config->GetName());
            
            // TODO: Implement dependency checking logic
            FMessageDialog::Open(EAppMsgType::Ok, 
                FText::Format(LOCTEXT("DependencyCheckPlaceholder", "Directory dependency check for: {0}\n(Implementation pending)"), 
                FText::FromString(Config->ConfigName)));
        }
    }
}

bool FNeoSkeletonPakConfigActions::CanExecutePackaging(TArray<TWeakObjectPtr<UNeoSkeletonPakConfig>> Objects) const
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoSkeletonPakConfig* Config = WeakObject.Get())
        {
            if (!Config->ValidateConfiguration())
            {
                return false;
            }
        }
    }
    return Objects.Num() > 0;
}

#undef LOCTEXT_NAMESPACE
