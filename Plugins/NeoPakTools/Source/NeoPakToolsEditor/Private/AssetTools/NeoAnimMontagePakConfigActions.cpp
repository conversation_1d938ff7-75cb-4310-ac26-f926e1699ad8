// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoAnimMontagePakConfigActions.h"
#include "NeoPakToolsEditor.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Misc/MessageDialog.h"

#define LOCTEXT_NAMESPACE "NeoAnimMontagePakConfigActions"

FText FNeoAnimMontagePakConfigActions::GetName() const
{
    return LOCTEXT("AssetTypeActions_NeoAnimMontagePakConfig", "Animation Montage Pack Config");
}

FColor FNeoAnimMontagePakConfigActions::GetTypeColor() const
{
    return FColor(64, 64, 255); // Dark Blue
}

UClass* FNeoAnimMontagePakConfigActions::GetSupportedClass() const
{
    return UNeoAnimMontagePakConfig::StaticClass();
}

uint32 FNeoAnimMontagePakConfigActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FNeoAnimMontagePakConfigActions::GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder)
{
    TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> AnimMontageConfigs = GetTypedWeakObjectPtrs<UNeoAnimMontagePakConfig>(InObjects);

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimMontagePakConfig_ExecutePackaging", "Execute Packaging"),
        LOCTEXT("AnimMontagePakConfig_ExecutePackagingTooltip", "Execute the packaging process for this animation montage configuration"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimMontagePakConfigActions::ExecutePackaging, AnimMontageConfigs),
            FCanExecuteAction::CreateSP(this, &FNeoAnimMontagePakConfigActions::CanExecutePackaging, AnimMontageConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimMontagePakConfig_ValidateConfig", "Validate Configuration"),
        LOCTEXT("AnimMontagePakConfig_ValidateConfigTooltip", "Validate the configuration settings"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimMontagePakConfigActions::ExecuteValidateConfig, AnimMontageConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimMontagePakConfig_CheckDependencies", "Check Directory Dependencies"),
        LOCTEXT("AnimMontagePakConfig_CheckDependenciesTooltip", "Check if all dependencies are in the correct directory"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimMontagePakConfigActions::ExecuteCheckDirectoryDependencies, AnimMontageConfigs)
        )
    );
}

bool FNeoAnimMontagePakConfigActions::CanExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects) const
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimMontagePakConfig* Config = WeakObject.Get())
        {
            if (!Config->ValidateConfiguration())
            {
                return false;
            }
        }
    }
    return true;
}

void FNeoAnimMontagePakConfigActions::ExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimMontagePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Executing packaging for animation montage config: %s"), *Config->GetName());
            
            if (Config->ExecutePackaging())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingSuccess", "Packaging completed successfully: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingFailed", "Packaging failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoAnimMontagePakConfigActions::ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimMontagePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating animation montage config: %s"), *Config->GetName());
            
            if (Config->ValidateConfiguration())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationSuccess", "Configuration is valid: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationFailed", "Configuration validation failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoAnimMontagePakConfigActions::ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoAnimMontagePakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimMontagePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking directory dependencies for animation montage config: %s"), *Config->GetName());
            
            // TODO: Implement dependency checking logic
            FMessageDialog::Open(EAppMsgType::Ok, 
                FText::Format(LOCTEXT("DependencyCheckPlaceholder", "Directory dependency check for: {0}\n(Implementation pending)"), 
                FText::FromString(Config->ConfigName)));
        }
    }
}

#undef LOCTEXT_NAMESPACE
