// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoPakConfigActionsBase.h"
#include "NeoPakToolsEditor.h"
#include "PakManager/NeoPakManager.h"
#include "Utils/NeoAssetDependencyResolver.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Misc/MessageDialog.h"
#include "Styling/AppStyle.h"
#include "ToolMenus.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Subsystems/AssetEditorSubsystem.h"
#include "Editor.h"

#define LOCTEXT_NAMESPACE "FNeoPakConfigActionsBase"

FText FNeoPakConfigActionsBase::GetName() const
{
    return GetAssetTypeName();
}

FColor FNeoPakConfigActionsBase::GetTypeColor() const
{
    return GetAssetTypeColor();
}

uint32 FNeoPakConfigActionsBase::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FNeoPakConfigActionsBase::GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder)
{
    TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> ConfigObjects = GetTypedWeakObjectPtrs<UNeoPakConfigAssetBase>(InObjects);

    // 执行打包
    MenuBuilder.AddMenuEntry(
        LOCTEXT("ExecutePackaging", "Execute Packaging"),
        LOCTEXT("ExecutePackagingTooltip", "Execute the packaging process for this configuration"),
        FSlateIcon(FAppStyle::GetAppStyleSetName(), "ContentBrowser.AssetActions.GenericFind"),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoPakConfigActionsBase::ExecutePackaging, ConfigObjects),
            FCanExecuteAction::CreateSP(this, &FNeoPakConfigActionsBase::CanExecutePackaging, ConfigObjects)
        )
    );

    // 验证配置
    MenuBuilder.AddMenuEntry(
        LOCTEXT("ValidateConfig", "Validate Configuration"),
        LOCTEXT("ValidateConfigTooltip", "Validate the configuration settings"),
        FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Check"),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoPakConfigActionsBase::ExecuteValidateConfig, ConfigObjects)
        )
    );

    // 检查目录依赖
    MenuBuilder.AddMenuEntry(
        LOCTEXT("CheckDependencies", "Check Directory Dependencies"),
        LOCTEXT("CheckDependenciesTooltip", "Check if all dependencies are in the correct directory"),
        FSlateIcon(FAppStyle::GetAppStyleSetName(), "ContentBrowser.AssetActions.OpenInExternalEditor"),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoPakConfigActionsBase::ExecuteCheckDirectoryDependencies, ConfigObjects)
        )
    );

    // 分隔符
    MenuBuilder.AddMenuSeparator();

    // 显示PAK信息
    MenuBuilder.AddMenuEntry(
        LOCTEXT("ShowPakInfo", "Show PAK Information"),
        LOCTEXT("ShowPakInfoTooltip", "Show information about the generated PAK file"),
        FSlateIcon(FAppStyle::GetAppStyleSetName(), "ContentBrowser.AssetActions.ViewDocumentation"),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoPakConfigActionsBase::ExecuteShowPakInfo, ConfigObjects),
            FCanExecuteAction::CreateSP(this, &FNeoPakConfigActionsBase::CanShowPakInfo, ConfigObjects)
        )
    );

    // 如果选择了多个对象，添加批量操作
    if (ConfigObjects.Num() > 1)
    {
        MenuBuilder.AddMenuSeparator();

        MenuBuilder.AddMenuEntry(
            LOCTEXT("BatchPackaging", "Batch Packaging"),
            LOCTEXT("BatchPackagingTooltip", "Execute packaging for all selected configurations"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "ContentBrowser.AssetActions.Duplicate"),
            FUIAction(
                FExecuteAction::CreateSP(this, &FNeoPakConfigActionsBase::ExecuteBatchPackaging, ConfigObjects),
                FCanExecuteAction::CreateSP(this, &FNeoPakConfigActionsBase::CanExecutePackaging, ConfigObjects)
            )
        );
    }
}

void FNeoPakConfigActionsBase::ExecutePackaging(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects)
{
    TArray<UNeoPakConfigAssetBase*> ValidConfigs = GetValidConfigs(Objects);
    
    if (ValidConfigs.Num() == 0)
    {
        ShowResultDialog(
            LOCTEXT("PackagingFailedTitle", "Packaging Failed"),
            LOCTEXT("NoValidConfigs", "No valid configurations found"),
            false
        );
        return;
    }

    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    if (!PakManager)
    {
        ShowResultDialog(
            LOCTEXT("PackagingFailedTitle", "Packaging Failed"),
            LOCTEXT("PakManagerNotAvailable", "PAK Manager is not available"),
            false
        );
        return;
    }

    int32 SuccessCount = 0;
    int32 FailureCount = 0;

    for (UNeoPakConfigAssetBase* Config : ValidConfigs)
    {
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Executing packaging for config: %s"), *Config->GetName());
        
        if (PakManager->PackageFromConfigWithDependencyCheck(Config, false))
        {
            SuccessCount++;
        }
        else
        {
            FailureCount++;
        }
    }

    // 显示结果
    FText ResultMessage = FText::Format(
        LOCTEXT("PackagingResultMessage", "Packaging completed.\nSuccessful: {0}\nFailed: {1}"),
        FText::AsNumber(SuccessCount),
        FText::AsNumber(FailureCount)
    );

    ShowResultDialog(
        LOCTEXT("PackagingResultTitle", "Packaging Result"),
        ResultMessage,
        FailureCount == 0
    );
}

void FNeoPakConfigActionsBase::ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects)
{
    TArray<UNeoPakConfigAssetBase*> ValidConfigs = GetValidConfigs(Objects);
    
    if (ValidConfigs.Num() == 0)
    {
        ShowResultDialog(
            LOCTEXT("ValidationFailedTitle", "Validation Failed"),
            LOCTEXT("NoValidConfigs", "No valid configurations found"),
            false
        );
        return;
    }

    int32 ValidCount = 0;
    int32 InvalidCount = 0;

    for (UNeoPakConfigAssetBase* Config : ValidConfigs)
    {
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating config: %s"), *Config->GetName());
        
#if WITH_EDITOR
        if (Config->ValidateConfiguration())
        {
            ValidCount++;
        }
        else
        {
            InvalidCount++;
        }
#else
        ValidCount++; // In non-editor builds, assume valid
#endif
    }

    // 显示结果
    FText ResultMessage = FText::Format(
        LOCTEXT("ValidationResultMessage", "Validation completed.\nValid: {0}\nInvalid: {1}"),
        FText::AsNumber(ValidCount),
        FText::AsNumber(InvalidCount)
    );

    ShowResultDialog(
        LOCTEXT("ValidationResultTitle", "Validation Result"),
        ResultMessage,
        InvalidCount == 0
    );
}

void FNeoPakConfigActionsBase::ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects)
{
    TArray<UNeoPakConfigAssetBase*> ValidConfigs = GetValidConfigs(Objects);
    
    if (ValidConfigs.Num() == 0)
    {
        ShowResultDialog(
            LOCTEXT("DependencyCheckFailedTitle", "Dependency Check Failed"),
            LOCTEXT("NoValidConfigs", "No valid configurations found"),
            false
        );
        return;
    }

    FNeoAssetDependencyResolver DependencyResolver;
    int32 PassedCount = 0;
    int32 FailedCount = 0;

    for (UNeoPakConfigAssetBase* Config : ValidConfigs)
    {
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking dependencies for config: %s"), *Config->GetName());
        
        FNeoDependencyCheckResult Result = DependencyResolver.ManualCheckDataAssetDependencies(Config);
        
        if (Result.bCheckPassed)
        {
            PassedCount++;
        }
        else
        {
            FailedCount++;
        }
    }

    // 显示结果
    FText ResultMessage = FText::Format(
        LOCTEXT("DependencyCheckResultMessage", "Dependency check completed.\nPassed: {0}\nFailed: {1}"),
        FText::AsNumber(PassedCount),
        FText::AsNumber(FailedCount)
    );

    ShowResultDialog(
        LOCTEXT("DependencyCheckResultTitle", "Dependency Check Result"),
        ResultMessage,
        FailedCount == 0
    );
}

void FNeoPakConfigActionsBase::ExecuteBatchPackaging(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects)
{
    // 批量打包就是调用普通打包，但会处理多个对象
    ExecutePackaging(Objects);
}

void FNeoPakConfigActionsBase::ExecuteShowPakInfo(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects)
{
    TArray<UNeoPakConfigAssetBase*> ValidConfigs = GetValidConfigs(Objects);

    if (ValidConfigs.Num() == 0)
    {
        ShowResultDialog(
            LOCTEXT("ShowPakInfoFailedTitle", "Show PAK Info Failed"),
            LOCTEXT("NoValidConfigs", "No valid configurations found"),
            false
        );
        return;
    }

    // 对于单个配置，显示详细信息
    if (ValidConfigs.Num() == 1)
    {
        UNeoPakConfigAssetBase* Config = ValidConfigs[0];
        FString PakFilePath = Config->GetFullOutputPath();

        // TODO: 实现PAK信息显示窗口
        // 这里可以打开一个专门的PAK信息窗口
        FText InfoMessage = FText::Format(
            LOCTEXT("PakInfoMessage", "PAK File Information:\nConfig: {0}\nOutput Path: {1}"),
            FText::FromString(Config->ConfigName),
            FText::FromString(PakFilePath)
        );

        ShowResultDialog(
            LOCTEXT("PakInfoTitle", "PAK Information"),
            InfoMessage,
            true
        );
    }
    else
    {
        // 对于多个配置，显示汇总信息
        FText InfoMessage = FText::Format(
            LOCTEXT("MultiplePakInfoMessage", "Selected {0} PAK configurations.\nUse single selection to view detailed information."),
            FText::AsNumber(ValidConfigs.Num())
        );

        ShowResultDialog(
            LOCTEXT("MultiplePakInfoTitle", "Multiple PAK Information"),
            InfoMessage,
            true
        );
    }
}

void FNeoPakConfigActionsBase::ExecuteOpenConfigEditor(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects)
{
    // 这个功能通常由引擎的默认双击行为处理
    // 这里可以添加自定义的配置编辑器逻辑
    for (auto& WeakObject : Objects)
    {
        if (UNeoPakConfigAssetBase* Config = WeakObject.Get())
        {
            // 使用引擎的默认资产编辑器（UE5新方式）
            if (UAssetEditorSubsystem* AssetEditorSubsystem = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>())
            {
                AssetEditorSubsystem->OpenEditorForAsset(Config);
            }
        }
    }
}

bool FNeoPakConfigActionsBase::CanExecutePackaging(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects) const
{
    TArray<UNeoPakConfigAssetBase*> ValidConfigs = GetValidConfigs(Objects);

    // 检查是否有有效的配置
    if (ValidConfigs.Num() == 0)
    {
        return false;
    }

#if WITH_EDITOR
    // 检查所有配置是否都有效
    for (UNeoPakConfigAssetBase* Config : ValidConfigs)
    {
        if (!Config->ValidateConfiguration())
        {
            return false;
        }
    }
#endif

    return true;
}

bool FNeoPakConfigActionsBase::CanShowPakInfo(TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>> Objects) const
{
    return GetValidConfigs(Objects).Num() > 0;
}

void FNeoPakConfigActionsBase::ShowResultDialog(const FText& Title, const FText& Message, bool bSuccess)
{
    EAppMsgType::Type MessageType = bSuccess ? EAppMsgType::Ok : EAppMsgType::Ok;
    FMessageDialog::Open(MessageType, Message, &Title);
}

TArray<UNeoPakConfigAssetBase*> FNeoPakConfigActionsBase::GetValidConfigs(const TArray<TWeakObjectPtr<UNeoPakConfigAssetBase>>& Objects) const
{
    TArray<UNeoPakConfigAssetBase*> ValidConfigs;

    for (const auto& WeakObject : Objects)
    {
        if (UNeoPakConfigAssetBase* Config = WeakObject.Get())
        {
            ValidConfigs.Add(Config);
        }
    }

    return ValidConfigs;
}

#undef LOCTEXT_NAMESPACE
