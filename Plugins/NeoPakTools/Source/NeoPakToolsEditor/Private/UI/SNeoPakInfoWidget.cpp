// Copyright Epic Games, Inc. All Rights Reserved.

#include "UI/SNeoPakInfoWidget.h"
#include "NeoPakToolsEditor.h"
#include "Utils/NeoDataAssetTypeRegistry.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Layout/SVerticalBox.h"
#include "Widgets/Layout/SHorizontalBox.h"
#include "Widgets/Layout/SSeparator.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "EditorStyleSet.h"

#define LOCTEXT_NAMESPACE "SNeoPakInfoWidget"

void SNeoPakInfoWidget::Construct(const FArguments& InArgs)
{
    PakFilePath = InArgs._PakFilePath;

    ChildSlot
    [
        CreateMainContent()
    ];

    RefreshContent();
}

TSharedRef<SWidget> SNeoPakInfoWidget::CreateMainContent()
{
    return SNew(SVerticalBox)
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("PakInfoTitle", "PAK File Information"))
            .Font(FCoreStyle::GetDefaultFontStyle("Bold", 14))
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SNew(SSeparator)
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("PakFilePathLabel", "File Path:"))
                .Font(FCoreStyle::GetDefaultFontStyle("Bold", 10))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(PakFilePathText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetFileStatusText)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("FileStatusLabel", "Status:"))
                .Font(FCoreStyle::GetDefaultFontStyle("Bold", 10))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(FileStatusText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetFileStatusText)
                .ColorAndOpacity(this, &SNeoPakInfoWidget::GetFileStatusColor)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("DataAssetTypeLabel", "DataAsset Type:"))
                .Font(FCoreStyle::GetDefaultFontStyle("Bold", 10))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(DataAssetTypeText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetDataAssetTypeText)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("TypeDescriptionLabel", "Description:"))
                .Font(FCoreStyle::GetDefaultFontStyle("Bold", 10))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(TypeDescriptionText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetTypeDescriptionText)
                .AutoWrapText(true)
            ]
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(SHorizontalBox)
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(0.0f, 0.0f, 10.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("SkeletonRequirementLabel", "Requires Skeleton:"))
                .Font(FCoreStyle::GetDefaultFontStyle("Bold", 10))
            ]
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SAssignNew(SkeletonRequirementText, STextBlock)
                .Text(this, &SNeoPakInfoWidget::GetSkeletonRequirementText)
            ]
        ];
}

void SNeoPakInfoWidget::RefreshContent()
{
    UpdateDisplayedInfo();
}

void SNeoPakInfoWidget::SetPakFilePath(const FString& InPakFilePath)
{
    PakFilePath = InPakFilePath;
    RefreshContent();
}

void SNeoPakInfoWidget::UpdateDisplayedInfo()
{
    // Force refresh of all text blocks by invalidating them
    if (PakFilePathText.IsValid())
    {
        PakFilePathText->Invalidate(EInvalidateWidget::Layout);
    }
    if (FileStatusText.IsValid())
    {
        FileStatusText->Invalidate(EInvalidateWidget::Layout);
    }
    if (DataAssetTypeText.IsValid())
    {
        DataAssetTypeText->Invalidate(EInvalidateWidget::Layout);
    }
    if (TypeDescriptionText.IsValid())
    {
        TypeDescriptionText->Invalidate(EInvalidateWidget::Layout);
    }
    if (SkeletonRequirementText.IsValid())
    {
        SkeletonRequirementText->Invalidate(EInvalidateWidget::Layout);
    }
}

FText SNeoPakInfoWidget::GetFileStatusText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoFileSelected", "No file selected");
    }

    bool bFileExists = FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath);
    if (bFileExists)
    {
        return LOCTEXT("FileExists", "File exists");
    }
    else
    {
        return LOCTEXT("FileNotFound", "File not found");
    }
}

FSlateColor SNeoPakInfoWidget::GetFileStatusColor() const
{
    if (PakFilePath.IsEmpty())
    {
        return FSlateColor::UseForeground();
    }

    bool bFileExists = FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath);
    if (bFileExists)
    {
        return FSlateColor(FLinearColor::Green);
    }
    else
    {
        return FSlateColor(FLinearColor::Red);
    }
}

FText SNeoPakInfoWidget::GetDataAssetTypeText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoTypeInfo", "N/A");
    }

    if (FNeoDataAssetTypeRegistry::HasDataAssetTypeInfo(PakFilePath))
    {
        FNeoDataAssetTypeInfo TypeInfo = FNeoDataAssetTypeRegistry::GetDataAssetTypeFromPak(PakFilePath);
        return FText::FromString(TypeInfo.TypeName);
    }
    else
    {
        return LOCTEXT("NoTypeInfoAvailable", "No type information available");
    }
}

FText SNeoPakInfoWidget::GetTypeDescriptionText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoDescription", "N/A");
    }

    if (FNeoDataAssetTypeRegistry::HasDataAssetTypeInfo(PakFilePath))
    {
        FNeoDataAssetTypeInfo TypeInfo = FNeoDataAssetTypeRegistry::GetDataAssetTypeFromPak(PakFilePath);
        return FText::FromString(TypeInfo.Description);
    }
    else
    {
        return LOCTEXT("NoDescriptionAvailable", "No description available");
    }
}

FText SNeoPakInfoWidget::GetSkeletonRequirementText() const
{
    if (PakFilePath.IsEmpty())
    {
        return LOCTEXT("NoRequirementInfo", "N/A");
    }

    if (FNeoDataAssetTypeRegistry::HasDataAssetTypeInfo(PakFilePath))
    {
        FNeoDataAssetTypeInfo TypeInfo = FNeoDataAssetTypeRegistry::GetDataAssetTypeFromPak(PakFilePath);
        return TypeInfo.bRequiresSkeleton ? LOCTEXT("RequiresSkeleton", "Yes") : LOCTEXT("NoSkeletonRequired", "No");
    }
    else
    {
        return LOCTEXT("NoRequirementInfoAvailable", "Unknown");
    }
}

#undef LOCTEXT_NAMESPACE
