// Copyright Epic Games, Inc. All Rights Reserved.

#include "UI/NeoPakToolsWindowManager.h"
#include "UI/SNeoPakManagerWindow.h"
#include "NeoPakToolsEditor.h"
#include "Framework/Docking/TabManager.h"
#include "Widgets/Docking/SDockTab.h"
#include "WorkspaceMenuStructure.h"
#include "WorkspaceMenuStructureModule.h"

#define LOCTEXT_NAMESPACE "FNeoPakToolsWindowManager"

// Static member definitions
const FName FNeoPakToolsWindowManager::PakManagerTabId = FName("NeoPakManagerTab");
TSharedPtr<SNeoPakManagerWindow> FNeoPakToolsWindowManager::PakManagerWindow;
bool FNeoPakToolsWindowManager::bIsInitialized = false;

void FNeoPakToolsWindowManager::Initialize()
{
    if (bIsInitialized)
    {
        return;
    }

    // Register the tab spawner
    FGlobalTabmanager::Get()->RegisterNomadTabSpawner(
        PakManagerTabId,
        FOnSpawnTab::CreateStatic(&FNeoPakToolsWindowManager::SpawnPakManagerTab)
    )
    .SetDisplayName(LOCTEXT("PakManagerTabTitle", "NeoPak Manager"))
    .SetTooltipText(LOCTEXT("PakManagerTabTooltip", "Open the NeoPakTools PAK Manager window"))
    .SetGroup(WorkspaceMenu::GetMenuStructure().GetDeveloperToolsCategory())
    .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "ContentBrowser.AssetActions.GenericFind"));

    bIsInitialized = true;

    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("NeoPakTools Window Manager initialized"));
}

void FNeoPakToolsWindowManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Unregister the tab spawner
    FGlobalTabmanager::Get()->UnregisterNomadTabSpawner(PakManagerTabId);

    // Clear references
    PakManagerWindow.Reset();

    bIsInitialized = false;

    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("NeoPakTools Window Manager shutdown"));
}

void FNeoPakToolsWindowManager::OpenPakManagerWindow()
{
    if (!bIsInitialized)
    {
        UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Window Manager not initialized"));
        return;
    }

    // Invoke the tab
    FGlobalTabmanager::Get()->TryInvokeTab(PakManagerTabId);
}

void FNeoPakToolsWindowManager::ClosePakManagerWindow()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Find and close the tab
    TSharedPtr<SDockTab> ExistingTab = FGlobalTabmanager::Get()->FindExistingLiveTab(PakManagerTabId);
    if (ExistingTab.IsValid())
    {
        ExistingTab->RequestCloseTab();
    }
}

bool FNeoPakToolsWindowManager::IsPakManagerWindowOpen()
{
    if (!bIsInitialized)
    {
        return false;
    }

    TSharedPtr<SDockTab> ExistingTab = FGlobalTabmanager::Get()->FindExistingLiveTab(PakManagerTabId);
    return ExistingTab.IsValid();
}

TSharedRef<SDockTab> FNeoPakToolsWindowManager::SpawnPakManagerTab(const FSpawnTabArgs& Args)
{
    // Create the PAK manager window if it doesn't exist
    if (!PakManagerWindow.IsValid())
    {
        SAssignNew(PakManagerWindow, SNeoPakManagerWindow);
    }

    // Create and return the dock tab
    return SNew(SDockTab)
        .TabRole(ETabRole::NomadTab)
        .Label(LOCTEXT("PakManagerTabTitle", "NeoPak Manager"))
        .ToolTipText(LOCTEXT("PakManagerTabTooltip", "NeoPakTools PAK Manager"))
        [
            PakManagerWindow.ToSharedRef()
        ];
}

#undef LOCTEXT_NAMESPACE
