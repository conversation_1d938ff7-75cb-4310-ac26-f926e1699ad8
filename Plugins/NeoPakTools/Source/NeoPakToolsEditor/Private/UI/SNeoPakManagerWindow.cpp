// Copyright Epic Games, Inc. All Rights Reserved.

#include "UI/SNeoPakManagerWindow.h"
#include "UI/SNeoPakInfoWidget.h"
#include "NeoPakToolsEditor.h"
#include "Config/NeoPakToolsSettings.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Views/STableRow.h"
#include "Widgets/Layout/SSeparator.h"
#include "Widgets/Layout/SSplitter.h"
#include "Widgets/Notifications/SProgressBar.h"
#include "Styling/AppStyle.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/Engine.h"

#define LOCTEXT_NAMESPACE "SNeoPakManagerWindow"

void SNeoPakManagerWindow::Construct(const FArguments& InArgs)
{
    PakManager = UNeoPakManager::GetInstance();

    ChildSlot
    [
        SNew(SVerticalBox)
        
        // Toolbar
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            CreateToolbar()
        ]

        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SNew(SSeparator)
        ]

        // Main content area
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(5.0f)
        [
            SNew(SSplitter)
            .Orientation(Orient_Horizontal)

            // Left panel - Configuration list
            + SSplitter::Slot()
            .Value(0.3f)
            [
                CreateConfigListPanel()
            ]

            // Middle panel - PAK list
            + SSplitter::Slot()
            .Value(0.3f)
            [
                CreatePakListPanel()
            ]

            // Right panel - Info
            + SSplitter::Slot()
            .Value(0.4f)
            [
                CreateInfoPanel()
            ]
        ]

        // Status bar
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            CreateStatusBar()
        ]
    ];

    RefreshAll();
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreateToolbar()
{
    return SNew(SHorizontalBox)

        // Package Selected button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(PackageSelectedButton, SButton)
            .Text(LOCTEXT("PackageSelected", "Package Selected"))
            .ToolTipText(LOCTEXT("PackageSelectedTooltip", "Package the selected configuration"))
            .OnClicked(this, &SNeoPakManagerWindow::OnPackageSelectedClicked)
            .IsEnabled(this, &SNeoPakManagerWindow::CanPackageSelected)
        ]

        // Package All button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(PackageAllButton, SButton)
            .Text(LOCTEXT("PackageAll", "Package All"))
            .ToolTipText(LOCTEXT("PackageAllTooltip", "Package all valid configurations"))
            .OnClicked(this, &SNeoPakManagerWindow::OnPackageAllClicked)
            .IsEnabled(this, &SNeoPakManagerWindow::CanPackageAll)
        ]

        // Validate All button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(ValidateAllButton, SButton)
            .Text(LOCTEXT("ValidateAll", "Validate All"))
            .ToolTipText(LOCTEXT("ValidateAllTooltip", "Validate all configurations"))
            .OnClicked(this, &SNeoPakManagerWindow::OnValidateAllClicked)
            .IsEnabled(this, &SNeoPakManagerWindow::CanValidateAll)
        ]

        // Spacer
        + SHorizontalBox::Slot()
        .FillWidth(1.0f)
        [
            SNullWidget::NullWidget
        ]

        // Clean Output button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(CleanOutputButton, SButton)
            .Text(LOCTEXT("CleanOutput", "Clean Output"))
            .ToolTipText(LOCTEXT("CleanOutputTooltip", "Clean the PAK output directory"))
            .OnClicked(this, &SNeoPakManagerWindow::OnCleanOutputClicked)
            .IsEnabled(this, &SNeoPakManagerWindow::CanCleanOutput)
        ]

        // Refresh button
        + SHorizontalBox::Slot()
        .AutoWidth()
        .Padding(2.0f)
        [
            SAssignNew(RefreshButton, SButton)
            .Text(LOCTEXT("Refresh", "Refresh"))
            .ToolTipText(LOCTEXT("RefreshTooltip", "Refresh all lists"))
            .OnClicked(this, &SNeoPakManagerWindow::OnRefreshClicked)
        ];
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreateConfigListPanel()
{
    return SNew(SVerticalBox)

        // Header
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("ConfigurationsHeader", "DataAsset Configurations"))
            .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
        ]

        // List view
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(5.0f)
        [
            SAssignNew(ConfigListView, SListView<TSharedPtr<UNeoPakConfigAssetBase*>>)
            .ListItemsSource(&ConfigAssets)
            .OnGenerateRow(this, &SNeoPakManagerWindow::OnGenerateConfigRow)
            .OnSelectionChanged(this, &SNeoPakManagerWindow::OnConfigSelectionChanged)
            .SelectionMode(ESelectionMode::Single)
        ];
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreatePakListPanel()
{
    return SNew(SVerticalBox)

        // Header
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("PakFilesHeader", "PAK Files"))
            .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
        ]

        // List view
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(5.0f)
        [
            SAssignNew(PakListView, SListView<TSharedPtr<FNeoPakEntry>>)
            .ListItemsSource(&PakEntries)
            .OnGenerateRow(this, &SNeoPakManagerWindow::OnGeneratePakRow)
            .OnSelectionChanged(this, &SNeoPakManagerWindow::OnPakSelectionChanged)
            .SelectionMode(ESelectionMode::Single)
        ];
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreateInfoPanel()
{
    return SNew(SVerticalBox)

        // Header
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(5.0f)
        [
            SNew(STextBlock)
            .Text(LOCTEXT("InfoHeader", "Information"))
            .Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
        ]

        // Info widget
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(5.0f)
        [
            SAssignNew(PakInfoWidget, SNeoPakInfoWidget)
        ];
}

TSharedRef<SWidget> SNeoPakManagerWindow::CreateStatusBar()
{
    return SNew(SVerticalBox)

        // Progress bar
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SAssignNew(ProgressBar, SProgressBar)
            .Percent(this, &SNeoPakManagerWindow::GetProgressPercent)
            .Visibility(EVisibility::Collapsed)
        ]

        // Status text
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2.0f)
        [
            SAssignNew(StatusText, STextBlock)
            .Text(this, &SNeoPakManagerWindow::GetStatusText)
        ];
}

void SNeoPakManagerWindow::RefreshAll()
{
    RefreshConfigList();
    RefreshPakList();
    UpdateButtonStates();
    UpdateStatusText(LOCTEXT("Ready", "Ready"));
}

TSharedRef<ITableRow> SNeoPakManagerWindow::OnGenerateConfigRow(TSharedPtr<UNeoPakConfigAssetBase*> Item, const TSharedRef<STableViewBase>& OwnerTable)
{
    UNeoPakConfigAssetBase* Config = Item.IsValid() ? *Item : nullptr;
    FString DisplayName = Config ? Config->ConfigName : TEXT("Invalid Config");

    return SNew(STableRow<TSharedPtr<UNeoPakConfigAssetBase*>>, OwnerTable)
        [
            SNew(STextBlock)
            .Text(FText::FromString(DisplayName))
            .ToolTipText(Config ? FText::FromString(Config->GetFullOutputPath()) : FText::GetEmpty())
        ];
}

TSharedRef<ITableRow> SNeoPakManagerWindow::OnGeneratePakRow(TSharedPtr<FNeoPakEntry> Item, const TSharedRef<STableViewBase>& OwnerTable)
{
    FString DisplayName = Item.IsValid() ? FPaths::GetCleanFilename(Item->PakFilePath) : TEXT("Invalid PAK");

    return SNew(STableRow<TSharedPtr<FNeoPakEntry>>, OwnerTable)
        [
            SNew(SHorizontalBox)

            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            [
                SNew(STextBlock)
                .Text(FText::FromString(DisplayName))
                .ToolTipText(Item.IsValid() ? FText::FromString(Item->PakFilePath) : FText::GetEmpty())
            ]

            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(5.0f, 0.0f)
            [
                SNew(STextBlock)
                .Text(Item.IsValid() ?
                    (Item->LoadStatus == EPakLoadStatus::Loaded ? LOCTEXT("Loaded", "Loaded") : LOCTEXT("NotLoaded", "Not Loaded")) :
                    FText::GetEmpty())
                .ColorAndOpacity(Item.IsValid() && Item->LoadStatus == EPakLoadStatus::Loaded ?
                    FSlateColor(FLinearColor::Green) : FSlateColor(FLinearColor::Gray))
            ]
        ];
}

void SNeoPakManagerWindow::OnConfigSelectionChanged(TSharedPtr<UNeoPakConfigAssetBase*> SelectedItem, ESelectInfo::Type SelectInfo)
{
    SelectedConfig = SelectedItem;
    UpdateButtonStates();

    // Update info panel
    if (SelectedItem.IsValid() && *SelectedItem)
    {
        FString PakPath = (*SelectedItem)->GetFullOutputPath();
        if (PakInfoWidget.IsValid())
        {
            PakInfoWidget->SetPakFilePath(PakPath);
        }
    }
}

void SNeoPakManagerWindow::OnPakSelectionChanged(TSharedPtr<FNeoPakEntry> SelectedItem, ESelectInfo::Type SelectInfo)
{
    SelectedPakEntry = SelectedItem;

    // Update info panel
    if (SelectedItem.IsValid() && PakInfoWidget.IsValid())
    {
        PakInfoWidget->SetPakFilePath(SelectedItem->PakFilePath);
    }
}

FReply SNeoPakManagerWindow::OnPackageSelectedClicked()
{
    if (SelectedConfig.IsValid() && *SelectedConfig && PakManager.IsValid())
    {
        UNeoPakConfigAssetBase* Config = *SelectedConfig;
        UpdateStatusText(FText::Format(LOCTEXT("PackagingConfig", "Packaging: {0}"), FText::FromString(Config->ConfigName)));
        ShowProgressBar(true);

        bool bSuccess = PakManager->PackageFromConfigWithDependencyCheck(Config, false);

        ShowProgressBar(false);
        UpdateStatusText(bSuccess ?
            FText::Format(LOCTEXT("PackagingSuccess", "Successfully packaged: {0}"), FText::FromString(Config->ConfigName)) :
            FText::Format(LOCTEXT("PackagingFailed", "Failed to package: {0}"), FText::FromString(Config->ConfigName)));

        RefreshPakList();
    }

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnPackageAllClicked()
{
    if (PakManager.IsValid())
    {
        UpdateStatusText(LOCTEXT("PackagingAll", "Packaging all configurations..."));
        ShowProgressBar(true);

        int32 SuccessCount = 0;
        int32 TotalCount = 0;

        for (const auto& ConfigPtr : ConfigAssets)
        {
            if (ConfigPtr.IsValid() && *ConfigPtr)
            {
                TotalCount++;
                UNeoPakConfigAssetBase* Config = *ConfigPtr;

                if (PakManager->PackageFromConfigWithDependencyCheck(Config, false))
                {
                    SuccessCount++;
                }
            }
        }

        ShowProgressBar(false);
        UpdateStatusText(FText::Format(LOCTEXT("PackagingAllResult", "Packaged {0}/{1} configurations"),
            FText::AsNumber(SuccessCount), FText::AsNumber(TotalCount)));

        RefreshPakList();
    }

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnValidateAllClicked()
{
    UpdateStatusText(LOCTEXT("ValidatingAll", "Validating all configurations..."));

    int32 ValidCount = 0;
    int32 TotalCount = 0;

    for (const auto& ConfigPtr : ConfigAssets)
    {
        if (ConfigPtr.IsValid() && *ConfigPtr)
        {
            TotalCount++;
            UNeoPakConfigAssetBase* Config = *ConfigPtr;

#if WITH_EDITOR
            if (Config->ValidateConfiguration())
            {
                ValidCount++;
            }
#else
            ValidCount++; // In non-editor builds, assume valid
#endif
        }
    }

    UpdateStatusText(FText::Format(LOCTEXT("ValidationResult", "Validation complete: {0}/{1} valid"),
        FText::AsNumber(ValidCount), FText::AsNumber(TotalCount)));

    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnRefreshClicked()
{
    RefreshAll();
    return FReply::Handled();
}

FReply SNeoPakManagerWindow::OnCleanOutputClicked()
{
    const UNeoPakToolsSettings* Settings = UNeoPakToolsSettings::GetNeoPakToolsSettings();
    if (Settings)
    {
        FString OutputDir = Settings->DefaultOutputDirectory.Path;
        if (!OutputDir.IsEmpty())
        {
            UpdateStatusText(FText::Format(LOCTEXT("CleaningOutput", "Cleaning output directory: {0}"), FText::FromString(OutputDir)));

            // TODO: Implement actual directory cleaning
            // For now, just show a message
            UpdateStatusText(LOCTEXT("CleaningComplete", "Output directory cleaning completed"));
        }
        else
        {
            UpdateStatusText(LOCTEXT("NoOutputDir", "No output directory specified in settings"));
        }
    }

    return FReply::Handled();
}

void SNeoPakManagerWindow::RefreshConfigList()
{
    ConfigAssets.Empty();

    // Find all DataAsset configurations in the project
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    TArray<FAssetData> AssetDataList;
    AssetRegistry.GetAssetsByClass(UNeoPakConfigAssetBase::StaticClass()->GetClassPathName(), AssetDataList, true);

    for (const FAssetData& AssetData : AssetDataList)
    {
        if (UNeoPakConfigAssetBase* Config = Cast<UNeoPakConfigAssetBase>(AssetData.GetAsset()))
        {
            ConfigAssets.Add(MakeShareable(new UNeoPakConfigAssetBase*(Config)));
        }
    }

    if (ConfigListView.IsValid())
    {
        ConfigListView->RequestListRefresh();
    }
}

void SNeoPakManagerWindow::RefreshPakList()
{
    PakEntries.Empty();

    if (PakManager.IsValid())
    {
        TArray<FNeoPakEntry> LoadedPaks = PakManager->GetLoadedPaks();
        for (const FNeoPakEntry& Entry : LoadedPaks)
        {
            PakEntries.Add(MakeShareable(new FNeoPakEntry(Entry)));
        }
    }

    if (PakListView.IsValid())
    {
        PakListView->RequestListRefresh();
    }
}

void SNeoPakManagerWindow::UpdateButtonStates()
{
    // Update button enabled states based on current selection and state
}

void SNeoPakManagerWindow::UpdateStatusText(const FText& NewStatus)
{
    if (StatusText.IsValid())
    {
        StatusText->SetText(NewStatus);
    }
}

void SNeoPakManagerWindow::ShowProgressBar(bool bShow)
{
    if (ProgressBar.IsValid())
    {
        ProgressBar->SetVisibility(bShow ? EVisibility::Visible : EVisibility::Collapsed);
    }
}

bool SNeoPakManagerWindow::CanPackageSelected() const
{
    return SelectedConfig.IsValid() && *SelectedConfig;
}

bool SNeoPakManagerWindow::CanPackageAll() const
{
    return ConfigAssets.Num() > 0;
}

bool SNeoPakManagerWindow::CanValidateAll() const
{
    return ConfigAssets.Num() > 0;
}

bool SNeoPakManagerWindow::CanCleanOutput() const
{
    const UNeoPakToolsSettings* Settings = UNeoPakToolsSettings::GetNeoPakToolsSettings();
    return Settings && !Settings->DefaultOutputDirectory.Path.IsEmpty();
}

FText SNeoPakManagerWindow::GetSelectedConfigText() const
{
    if (SelectedConfig.IsValid() && *SelectedConfig)
    {
        return FText::FromString((*SelectedConfig)->ConfigName);
    }
    return LOCTEXT("NoConfigSelected", "No configuration selected");
}

FText SNeoPakManagerWindow::GetSelectedPakText() const
{
    if (SelectedPakEntry.IsValid())
    {
        return FText::FromString(FPaths::GetCleanFilename(SelectedPakEntry->PakFilePath));
    }
    return LOCTEXT("NoPakSelected", "No PAK selected");
}

FText SNeoPakManagerWindow::GetStatusText() const
{
    return LOCTEXT("Ready", "Ready");
}

TOptional<float> SNeoPakManagerWindow::GetProgressPercent() const
{
    // For now, return indeterminate progress
    return TOptional<float>();
}

#undef LOCTEXT_NAMESPACE
