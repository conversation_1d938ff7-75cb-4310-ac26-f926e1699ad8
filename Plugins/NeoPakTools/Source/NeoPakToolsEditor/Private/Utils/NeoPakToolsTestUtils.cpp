// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoPakToolsTestUtils.h"
#include "NeoPakToolsEditor.h"
#include "Config/NeoSkeletonPakConfig.h"
#include "Config/NeoCharacterPakConfig.h"
#include "Config/NeoMapPakConfig.h"
#include "PakManager/NeoPakManager.h"
#include "Utils/NeoPakFileCreator.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Engine/Engine.h"

// Static member definition
FString FNeoPakToolsTestUtils::TestDirectoryPath;

void FNeoPakToolsTestUtils::CreateSampleConfigurations()
{
    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Creating sample configurations for testing..."));

    // Create skeleton config first
    UNeoSkeletonPakConfig* SkeletonConfig = CreateSampleSkeletonConfig();
    if (SkeletonConfig)
    {
        LogTestResult(TEXT("Create Skeleton Config"), true, SkeletonConfig->GetName());
    }
    else
    {
        LogTestResult(TEXT("Create Skeleton Config"), false, TEXT("Failed to create skeleton config"));
        return;
    }

    // Create character config
    UNeoCharacterPakConfig* CharacterConfig = CreateSampleCharacterConfig(SkeletonConfig);
    if (CharacterConfig)
    {
        LogTestResult(TEXT("Create Character Config"), true, CharacterConfig->GetName());
    }
    else
    {
        LogTestResult(TEXT("Create Character Config"), false, TEXT("Failed to create character config"));
    }

    // Create map config
    UNeoMapPakConfig* MapConfig = CreateSampleMapConfig();
    if (MapConfig)
    {
        LogTestResult(TEXT("Create Map Config"), true, MapConfig->GetName());
    }
    else
    {
        LogTestResult(TEXT("Create Map Config"), false, TEXT("Failed to create map config"));
    }

    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Sample configuration creation completed"));
}

bool FNeoPakToolsTestUtils::TestPakCreation()
{
    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Testing PAK creation functionality..."));

    // Test UnrealPak availability
    bool bUnrealPakAvailable = FNeoPakFileCreator::IsUnrealPakAvailable();
    LogTestResult(TEXT("UnrealPak Availability"), bUnrealPakAvailable, 
        bUnrealPakAvailable ? FNeoPakFileCreator::GetUnrealPakPath() : TEXT("UnrealPak not found"));

    if (!bUnrealPakAvailable)
    {
        UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("UnrealPak not available, skipping PAK creation test"));
        return false;
    }

    // Test with sample assets
    TArray<FString> TestAssets;
    TestAssets.Add(TEXT("/Game/TestAsset1"));
    TestAssets.Add(TEXT("/Game/TestAsset2"));

    FString TestOutputPath = GetTestDirectory() / TEXT("TestPak.pak");
    
    // Note: This will likely fail because the test assets don't exist,
    // but it tests the PAK creation pipeline
    bool bPakCreated = FNeoPakFileCreator::CreatePakFromAssets(TestAssets, TestOutputPath);
    LogTestResult(TEXT("PAK Creation Test"), bPakCreated, TestOutputPath);

    return bPakCreated;
}

bool FNeoPakToolsTestUtils::ValidatePluginComponents()
{
    UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating plugin components..."));

    bool bAllValid = true;

    // Test PAK Manager
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    bool bPakManagerValid = (PakManager != nullptr);
    LogTestResult(TEXT("PAK Manager"), bPakManagerValid, bPakManagerValid ? TEXT("Instance created") : TEXT("Failed to create instance"));
    bAllValid &= bPakManagerValid;

    // Test settings
    const UNeoPakToolsSettings* Settings = UNeoPakToolsSettings::GetNeoPakToolsSettings();
    bool bSettingsValid = (Settings != nullptr);
    LogTestResult(TEXT("Plugin Settings"), bSettingsValid, bSettingsValid ? TEXT("Settings loaded") : TEXT("Failed to load settings"));
    bAllValid &= bSettingsValid;

    // Test asset registry
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    bool bAssetRegistryValid = AssetRegistryModule.Get().IsLoadingAssets() || AssetRegistryModule.Get().IsSearchAllAssets();
    LogTestResult(TEXT("Asset Registry"), bAssetRegistryValid, TEXT("Asset registry accessible"));
    bAllValid &= bAssetRegistryValid;

    LogTestResult(TEXT("Overall Plugin Validation"), bAllValid, 
        bAllValid ? TEXT("All components valid") : TEXT("Some components failed validation"));

    return bAllValid;
}

UNeoSkeletonPakConfig* FNeoPakToolsTestUtils::CreateSampleSkeletonConfig()
{
    // For testing purposes, we'll create a minimal config
    // In a real scenario, this would be created through the editor
    UNeoSkeletonPakConfig* Config = NewObject<UNeoSkeletonPakConfig>();
    if (Config)
    {
        Config->ConfigName = TEXT("TestSkeletonConfig");
        // Note: SkeletonAsset would need to be set to a real skeleton asset for full functionality
    }
    return Config;
}

UNeoCharacterPakConfig* FNeoPakToolsTestUtils::CreateSampleCharacterConfig(UNeoSkeletonPakConfig* SkeletonConfig)
{
    if (!SkeletonConfig)
    {
        return nullptr;
    }

    UNeoCharacterPakConfig* Config = NewObject<UNeoCharacterPakConfig>();
    if (Config)
    {
        Config->ConfigName = TEXT("TestCharacterConfig");
        Config->RequiredSkeletonConfig = SkeletonConfig;
        // Note: CharacterBlueprint would need to be set to a real blueprint for full functionality
    }
    return Config;
}

UNeoMapPakConfig* FNeoPakToolsTestUtils::CreateSampleMapConfig()
{
    UNeoMapPakConfig* Config = NewObject<UNeoMapPakConfig>();
    if (Config)
    {
        Config->ConfigName = TEXT("TestMapConfig");
        // Note: Map would need to be set to a real map asset for full functionality
    }
    return Config;
}

FString FNeoPakToolsTestUtils::GetTestDirectory()
{
    if (TestDirectoryPath.IsEmpty())
    {
        TestDirectoryPath = FPaths::ProjectSavedDir() / TEXT("NeoPakToolsTests");
        
        // Ensure directory exists
        if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*TestDirectoryPath))
        {
            FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*TestDirectoryPath);
        }
    }
    return TestDirectoryPath;
}

void FNeoPakToolsTestUtils::CleanupTestFiles()
{
    if (!TestDirectoryPath.IsEmpty() && FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*TestDirectoryPath))
    {
        FPlatformFileManager::Get().GetPlatformFile().DeleteDirectoryRecursively(*TestDirectoryPath);
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Cleaned up test directory: %s"), *TestDirectoryPath);
    }
}

void FNeoPakToolsTestUtils::LogTestResult(const FString& TestName, bool bSuccess, const FString& Details)
{
    FString ResultText = bSuccess ? TEXT("PASSED") : TEXT("FAILED");
    FString LogMessage = FString::Printf(TEXT("[TEST] %s: %s"), *TestName, *ResultText);
    
    if (!Details.IsEmpty())
    {
        LogMessage += FString::Printf(TEXT(" - %s"), *Details);
    }

    if (bSuccess)
    {
        UE_LOG(LogNeoPakToolsEditor, Log, TEXT("%s"), *LogMessage);
    }
    else
    {
        UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("%s"), *LogMessage);
    }
}
