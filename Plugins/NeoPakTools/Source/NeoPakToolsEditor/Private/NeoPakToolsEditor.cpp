// Copyright Epic Games, Inc. All Rights Reserved.

#include "NeoPakToolsEditor.h"
#include "AssetTools/NeoSkeletonPakConfigActions.h"
#include "AssetTools/NeoCharacterPakConfigActions.h"
#include "AssetTools/NeoClothingPakConfigActions.h"
#include "AssetTools/NeoMapPakConfigActions.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"

#define LOCTEXT_NAMESPACE "FNeoPakToolsEditorModule"

DEFINE_LOG_CATEGORY(LogNeoPakToolsEditor);

void FNeoPakToolsEditorModule::StartupModule()
{
	UE_LOG(LogNeoPakToolsEditor, Log, TEXT("NeoPakToolsEditor module started"));
	
	// Register asset type actions
	RegisterAssetTypeActions();
}

void FNeoPakToolsEditorModule::ShutdownModule()
{
	UE_LOG(LogNeoPakToolsEditor, Log, TEXT("NeoPakToolsEditor module shutdown"));
	
	// Unregister asset type actions
	UnregisterAssetTypeActions();
}

void FNeoPakToolsEditorModule::RegisterAssetTypeActions()
{
	IAssetTools& AssetTools = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools").Get();
	
	// Register skeleton config actions
	TSharedRef<IAssetTypeActions> SkeletonActions = MakeShareable(new FNeoSkeletonPakConfigActions);
	AssetTools.RegisterAssetTypeActions(SkeletonActions);
	CreatedAssetTypeActions.Add(SkeletonActions);
	
	// Register character config actions
	TSharedRef<IAssetTypeActions> CharacterActions = MakeShareable(new FNeoCharacterPakConfigActions);
	AssetTools.RegisterAssetTypeActions(CharacterActions);
	CreatedAssetTypeActions.Add(CharacterActions);
	
	// Register clothing config actions
	TSharedRef<IAssetTypeActions> ClothingActions = MakeShareable(new FNeoClothingPakConfigActions);
	AssetTools.RegisterAssetTypeActions(ClothingActions);
	CreatedAssetTypeActions.Add(ClothingActions);
	
	// Register map config actions
	TSharedRef<IAssetTypeActions> MapActions = MakeShareable(new FNeoMapPakConfigActions);
	AssetTools.RegisterAssetTypeActions(MapActions);
	CreatedAssetTypeActions.Add(MapActions);
}

void FNeoPakToolsEditorModule::UnregisterAssetTypeActions()
{
	FAssetToolsModule* AssetToolsModule = FModuleManager::GetModulePtr<FAssetToolsModule>("AssetTools");
	if (AssetToolsModule)
	{
		IAssetTools& AssetTools = AssetToolsModule->Get();
		for (int32 Index = 0; Index < CreatedAssetTypeActions.Num(); ++Index)
		{
			AssetTools.UnregisterAssetTypeActions(CreatedAssetTypeActions[Index]);
		}
	}
	CreatedAssetTypeActions.Empty();
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FNeoPakToolsEditorModule, NeoPakToolsEditor)
