// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/NeoPakToolsCommands.h"

#define LOCTEXT_NAMESPACE "FNeoPakToolsCommands"

void FNeoPakToolsCommands::RegisterCommands()
{
    UI_COMMAND(OpenPakToolsWindow, "Open PAK Tools", "Open the NeoPakTools window", EUserInterfaceActionType::Button, FInputChord());
    UI_COMMAND(BatchPackageAll, "Batch Package All", "Package all valid configurations", EUserInterfaceActionType::Button, FInputChord());
    UI_COMMAND(ValidateAllConfigs, "Validate All Configs", "Validate all PAK configurations", EUserInterfaceActionType::Button, FInputChord());
    UI_COMMAND(CleanOutputDirectory, "Clean Output Directory", "Clean the PAK output directory", EUserInterfaceActionType::Button, FInputChord());
    UI_COMMAND(ShowPakInfo, "Show PAK Info", "Show information about selected PAK file", EUserInterfaceActionType::Button, FInputChord());
    UI_COMMAND(RefreshPakList, "Refresh PAK List", "Refresh the list of PAK files", EUserInterfaceActionType::Button, FInputChord());
}

#undef LOCTEXT_NAMESPACE
