<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Asio C++ Library</Name>
  <!-- Software Name and Version  -->
  <!-- Software Name: Asio C++ Library
    Download Link: https://sourceforge.net/projects/asio/files/asio/1.30.2%20%28Stable%29/
    Version: 1.30.2
    Notes: Currently used by the developer TraceAnalysis module in UE
  -->
<Location>//UE5/Main/Engine/Source/ThirdParty/asio</Location>
<Function>Asio is a cross-platform C++ library for network and low-level I/O programming that provides developers with a consistent asynchronous model using a modern C++ approach.</Function>
<Eula>https://github.com/chris<PERSON><PERSON><PERSON>/asio/blob/master/asio/LICENSE_1_0.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensee</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>